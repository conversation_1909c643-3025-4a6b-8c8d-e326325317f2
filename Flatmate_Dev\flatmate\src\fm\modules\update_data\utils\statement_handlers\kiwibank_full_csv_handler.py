from dataclasses import dataclass

import pandas as pd

from ._base_statement_handler import StatementHandler
from fm.core.standards.fm_standard_columns import StandardColumns


@dataclass
class KiwibankFullCSVHandler(StatementHandler):
    def __post_init__(self):
        self.statement_format = self.StatementFormat(
            bank_name="Kiwibank",
            variant="full",
            file_type="csv",
        )
        self.column_attrs = self.ColumnAttributes(
            has_col_names=True,  # Validate header names for full CSV
            has_account_column=True,  # Account number is in source data
            col_names_in_header=True,  # Column names are in the header section
            source_col_names=[
                "Account number",  
                "Date",
                "Memo/Description",
                "Source Code (payment type)",
                "TP ref",
                "TP part",
                "TP code",
                "OP ref",
                "OP part", 
                "OP code",
                "OP name",
                "OP Bank Account Number",
                "Amount (credit)",
                "Amount (debit)",
                "Amount",
                "Balance"
            ],
            target_col_names=[
                StandardColumns.ACCOUNT,
                StandardColumns.DATE,
                StandardColumns.DETAILS,
                StandardColumns.PAYMENT_TYPE,
                StandardColumns.TP_REF,
                StandardColumns.TP_PART,
                StandardColumns.TP_CODE,
                StandardColumns.OP_REF,
                StandardColumns.OP_PART,
                StandardColumns.OP_CODE,
                StandardColumns.OP_NAME,  
                StandardColumns.OP_ACCOUNT,
                StandardColumns.CREDIT_AMOUNT,
                StandardColumns.DEBIT_AMOUNT,
                StandardColumns.AMOUNT,
                StandardColumns.BALANCE
            ],
            n_source_cols=16,
            date_format='%d/%m/%y',  # Kiwibank Full CSV uses DD/MM/YY format (e.g., '18/08/24')
        )
        self.account_num_attrs = self.AccountNumberAttributes(
            pattern=r"38-?\d{4}-?\d{7}-?\d{2,3}.*",  # Match 2-3 digits at end
            in_data=True,  # Check first row data
            in_header=False,  # Don't check column names
            location=(0, 0),  # First row, first column
        )
        self.source_metadata_attrs = self.SourceMetadataAttributes(
            has_metadata_rows=False
        )

    def can_handle_file(self, df: pd.DataFrame) -> bool:
        """Check if this is a Kiwibank full CSV format."""
        # Full format has 16 columns
        if len(df.columns) != 16:
            return False
        
        # Check if first row contains account number
        first_row = df.iloc[0]
        return any('Account' in str(cell) for cell in first_row)