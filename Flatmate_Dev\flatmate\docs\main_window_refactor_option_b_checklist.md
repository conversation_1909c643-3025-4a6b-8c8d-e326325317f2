# MainWindow – Option B Refactor Checklist

_This checklist captures the **exact tasks** required for the minimal, low-risk refactor (Option B) that splits responsibilities while keeping current behaviour intact._

## 1  Pre-Refactor Safety

- [X] Ensure **working branch** created (e.g. `feat/main-window-refactor-b`).
- [X] Commit current state to Git (`git commit -am "pre-refactor baseline before Option B"`).
- [ ] Verify app launches with no regressions.

## 2  File & Naming Strategy

| Old Function / Logic        | New Name (or Location)     | Notes                                                               |
| --------------------------- | -------------------------- | ------------------------------------------------------------------- |
| `_setup_central_widget()` | `_setup_main_layout()`   | Public helper called from `setup_ui()`.                           |
|                             | `_create_title_bar()`    | Returns `CustomTitleBar`; **private**.                      |
|                             | `_create_content_area()` | Returns `(content_container, content_layout)`; **private**. |
| `_apply_window_styles()`  | `_set_global_styles()`   | (Optional rename for clarity).                                      |

## 3  Step-By-Step Refactor

1. **Move frameless flags**
   ```python
   # in __init__ BEFORE super().__init__ returns
   super().__init__(None, Qt.Window | Qt.FramelessWindowHint)
   self.setAttribute(Qt.WA_TranslucentBackground)
   ```
2. **Rename & stub new helpers** in `main_window.py` _without altering logic_.
3. **Cut-and-paste** blocks:
   * Title-bar creation ➜ `_create_title_bar()`.
   * Content container & layout ➜ `_create_content_area()`.
4. Update `_setup_main_layout()` to call the two helpers and add stretch factor `1` to the content widget.
5. **Delete** the original `_setup_central_widget()`.
6. **Search & replace** call-sites (`setup_ui()` etc.).
7. **Margin audit**
   * Ensure `self.main_layout.setContentsMargins(0,0,0,0)`.
   * Ensure `content_layout` margins `0`.
   * Confirm `CustomTitleBar.main_layout.setContentsMargins(10,0,5,0)` (only horizontal where required).
8. **Background colour rationalisation**
   * Retain dark theme in `_set_global_styles()`.
   * Remove duplicate `background-color` from `CustomTitleBar` if still present.
9. **Run & manually test**
   * Title bar flush to top, fixed 30 px height.
   * No black bands on resize.
   * Window controls work.
10. **Unit / smoke tests** (optional)
    * Assert splitter sizes preserved.
    * QTest drag on title bar moves window.
11. **Commit** (`git commit -am "Option B refactor – split main layout helpers"`).

## 4  Post-Refactor TODOs

- [ ] Update developer docs (`UI_ARCHITECTURE.md`) with new helper names.
- [ ] Open ticket to spike **Option C** (`MainWindowLayoutBuilder`).

---

_Once all checklist items are ticked off and tests pass, Option B refactor is complete. We will then have a cleaner foundation for future features (detachable panes, layout builder, etc.)._
