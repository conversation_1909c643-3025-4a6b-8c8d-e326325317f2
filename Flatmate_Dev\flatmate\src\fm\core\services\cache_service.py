"""
Cache service for preserving data across navigation.
"""

import os
import tempfile
import pickle
from typing import Any, Dict
import pandas as pd

from .logger import log, log_this, LogLevel


class CacheService:
    """
    Service for caching data across navigation.
    
    Features:
    - In-memory cache for fast access
    - Optional disk persistence for larger datasets
    """
    
    _instance = None
    
    def __new__(cls):
        """Singleton pattern implementation."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize the cache service."""
        if not hasattr(self, 'initialized'):
            self._memory_cache: Dict[str, Any] = {}
            self._disk_cache_paths: Dict[str, str] = {}
            self._use_disk_cache = True
            self._temp_dir = None
            
            if self._use_disk_cache:
                self._temp_dir = tempfile.mkdtemp(prefix="flatmate_cache_")
                
            self.initialized = True
            log("CacheService initialized", "d")
    
    @log_this(LogLevel.DEBUG)
    def put(self, key: str, value: Any, force_disk: bool = False) -> None:
        """
        Store a value in the cache.
        
        Args:
            key: Cache key
            value: Value to store
            force_disk: Force storage on disk even for small objects
        """
        # For DataFrames or large objects, use disk cache
        if self._use_disk_cache and (force_disk or 
                                    isinstance(value, pd.DataFrame) or 
                                    isinstance(value, (list, dict)) and len(str(value)) > 10000):
            self._store_on_disk(key, value)
        else:
            self._memory_cache[key] = value
    
    @log_this(LogLevel.DEBUG)
    def get(self, key: str, default: Any = None) -> Any:
        """
        Retrieve a value from the cache.
        
        Args:
            key: Cache key
            default: Default value if key not found
            
        Returns:
            Cached value or default
        """
        # Check memory cache first
        if key in self._memory_cache:
            return self._memory_cache[key]
        
        # Check disk cache
        if key in self._disk_cache_paths:
            return self._load_from_disk(key)
        
        return default
    
    def has(self, key: str) -> bool:
        """
        Check if a key exists in the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key exists, False otherwise
        """
        return key in self._memory_cache or key in self._disk_cache_paths
    
    @log_this(LogLevel.DEBUG)
    def remove(self, key: str) -> None:
        """
        Remove a value from the cache.
        
        Args:
            key: Cache key to remove
        """
        if key in self._memory_cache:
            del self._memory_cache[key]
        
        if key in self._disk_cache_paths:
            path = self._disk_cache_paths[key]
            if os.path.exists(path):
                os.remove(path)
            del self._disk_cache_paths[key]
    
    @log_this(LogLevel.INFO)
    def clear(self) -> None:
        """Clear all cached values."""
        self._memory_cache.clear()
        
        # Remove disk cache files
        for path in self._disk_cache_paths.values():
            if os.path.exists(path):
                os.remove(path)
        
        self._disk_cache_paths.clear()
    
    def _store_on_disk(self, key: str, value: Any) -> None:
        """Store a value on disk."""
        if not self._temp_dir:
            self._temp_dir = tempfile.mkdtemp(prefix="flatmate_cache_")
        
        # Create a unique filename
        filename = f"{key.replace('/', '_').replace(':', '_')}.pickle"
        path = os.path.join(self._temp_dir, filename)
        
        # Store the value
        with open(path, 'wb') as f:
            pickle.dump(value, f)
        
        # Remember the path
        self._disk_cache_paths[key] = path
    
    def _load_from_disk(self, key: str) -> Any:
        """Load a value from disk."""
        path = self._disk_cache_paths.get(key)
        if not path or not os.path.exists(path):
            return None
        
        # Load the value
        with open(path, 'rb') as f:
            return pickle.load(f)
    
    def __del__(self):
        """Clean up temporary files on deletion."""
        self.clear()
        
        # Remove temp directory
        if self._temp_dir and os.path.exists(self._temp_dir):
            try:
                os.rmdir(self._temp_dir)
            except OSError:
                # Directory not empty, ignore
                pass


# Singleton instance
cache_service = CacheService()