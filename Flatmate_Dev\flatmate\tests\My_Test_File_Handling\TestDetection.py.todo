from test_config import TestConfig
from ...src.fm.modules.update_data.utils.detection.format_detector import detect_bank_format

# Create an instance of TestConfig
test_config = TestConfig()

# Use the instance method to get test dataframes
test_files = test_config.get_test_dfs()

detected_formats = []

for df in test_files: 
    detected_formats.append(detect_bank_format(df))
    
print(detected_formats)