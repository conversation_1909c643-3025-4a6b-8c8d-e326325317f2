import os
import re
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

import pandas as pd
from google.cloud import vision

try:
    from PySide6.QtWidgets import (QApplication, QFileDialog, QMessageBox,
                                   QWidget)
except ImportError as e:
    print(f"Required dependency not found: {str(e)}")
    print("Please install required dependencies:")
    print("pip install PySide6 google-cloud-vision pandas")
    sys.exit(1)

def select_image_files(initial_dir: Optional[str] = None) -> List[str]:
    app = QApplication.instance() or QApplication(sys.argv)
    
    file_paths, _ = QFileDialog.getOpenFileNames(
        None, 
        "Select Receipt Images", 
        str(Path.home() if initial_dir is None else initial_dir), 
        "Image Files (*.png *.jpg *.jpeg *.bmp *.tiff)"
    )
    
    return file_paths

def extract_text_google_vision(image_path: str) -> List[Dict[str, Any]]:
    print(f"\nProcessing receipt: {image_path}")
    
    try:
        credentials_path = os.path.expanduser('~/DEV/a_CODING_PROJECTS/a_flatmate_App_DEV/ocr_key/original-bolt-449305-k2-6eb9df21e031.json')
        client = vision.ImageAnnotatorClient.from_service_account_file(credentials_path)
        
        with open(image_path, 'rb') as image_file:
            content = image_file.read()
        
        image = vision.Image(content=content)
        response = client.annotate_image(request=vision.AnnotateImageRequest(
            image=image,
            features=[vision.Feature(type_=vision.Feature.Type.TEXT_DETECTION)],
            image_context=vision.ImageContext(language_hints=['en'])
        ))
        
        if response.error.message:
            print(f"Error: {response.error.message}")
            return []
            
        texts = response.text_annotations
        if not texts:
            print("No text found")
            return []

        # Get raw text and split into lines
        full_text = texts[0].description
        lines = [line.strip() for line in full_text.split('\n') if line.strip()]
        
        # Determine receipt type from header
        is_bunnings = any('BUNNINGS' in line.upper() for line in lines[:5])
        is_resene = any('RESENE' in line.upper() for line in lines[:5])
        
        processed_lines = []
        i = 0
        while i < len(lines):
            line = lines[i]
            
            if is_bunnings:
                # Look for Bunnings item pattern (barcode + description)
                if re.match(r'^\d{13}\s+.+', line):
                    desc = re.sub(r'^\d{13}\s+', '', line)
                    
                    # Look ahead for price
                    price = ''
                    for j in range(i+1, min(i+3, len(lines))):
                        price_line = lines[j]
                        if '$' in price_line and 'D' in price_line:
                            price_match = re.search(r'\$(\d+\.\d{2})', price_line)
                            if price_match:
                                price = price_match.group(1)
                                i = j  # Skip to price line
                                break
                    
                    if price:
                        processed_lines.append({
                            'text': [desc, price],
                            'section': 'items'
                        })
                
            elif is_resene:
                # Look for Resene quantity and price pattern (e.g., "2 35.40")
                if re.match(r'^\d+\s+\d+\.\d{2}\s*$', line):
                    parts = line.split()
                    qty = parts[0]
                    unit_price = parts[1]
                    
                    # Look back for description (should be 2 lines back, after product code)
                    if i >= 2:
                        product_code = lines[i-1]  # e.g., "1470101"
                        desc = lines[i-2]          # e.g., "Bianca"
                        
                        # Look ahead for total
                        if i + 1 < len(lines):
                            total = lines[i+1]     # e.g., "70.80"
                            if re.match(r'^\d+\.\d{2}$', total):
                                processed_lines.append({
                                    'text': [f"{desc} ({qty})", total],
                                    'section': 'items'
                                })
                                i += 1  # Skip the total line
            
            # Store any unmatched lines
            if not processed_lines or processed_lines[-1].get('text')[0] != line:
                processed_lines.append({
                    'text': [line],
                    'section': 'raw'
                })
            
            i += 1
        
        print(f"Found {len(processed_lines)} lines")
        return [{
            'text': full_text,
            'confidence': 1.0,
            'rows': processed_lines
        }]
            
    except Exception as e:
        print(f"Error: {str(e)}")
        return []

def process_images_to_csv(
    image_paths: List[str], 
    output_path: Optional[str] = None
) -> pd.DataFrame:
    all_rows = []
    receipt_counter = 1
    
    for image_path in image_paths:
        try:
            text_results = extract_text_google_vision(image_path)
            
            if text_results and len(text_results) > 0:
                result = text_results[0]
                
                # Process each line
                for line in result.get('rows', []):
                    text_parts = line.get('text', [])
                    if len(text_parts) == 2:  # Item with price
                        all_rows.append({
                            'RECEIPT #': receipt_counter,
                            'ITEM': text_parts[0],
                            'PRICE': text_parts[1]
                        })
                    else:  # Other text
                        all_rows.append({
                            'RECEIPT #': receipt_counter,
                            'ITEM': text_parts[0],
                            'PRICE': ''
                        })
                
                # Add a blank row between receipts
                all_rows.append({
                    'RECEIPT #': receipt_counter,
                    'ITEM': '',
                    'PRICE': ''
                })
                
                receipt_counter += 1
            
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
    
    # Convert to DataFrame and save
    if all_rows:
        df = pd.DataFrame(all_rows)
        
        if output_path:
            df.to_csv(output_path, index=False)
            print(f"\nResults saved to: {output_path}")
            print(f"Found {len(df)} lines across {df['RECEIPT #'].nunique()} receipts")
        
        return df
    
    return pd.DataFrame()

def show_error(message: str, parent: QWidget | None = None) -> None:
    app = QApplication.instance() or QApplication(sys.argv)
    if parent is None:
        parent = QWidget()
    QMessageBox.critical(parent, "Error", message)
    if parent and not parent.parent():
        parent.deleteLater()

def main() -> None:
    try:
        # Select image files
        selected_images = select_image_files()
        
        # Process selected images
        if selected_images:
            # Use the directory of the first image for output
            first_image_path = Path(selected_images[0])
            output_path = first_image_path.parent / 'receipt_items.csv'
            
            print(f"\nProcessing {len(selected_images)} receipts...")
            print(f"Output will be saved to: {output_path}")
            
            # Process the images and save results
            results_df = process_images_to_csv(selected_images, str(output_path))
            
            if not results_df.empty:
                print("\nProcessing completed successfully!")
                print(f"Results saved to: {output_path}")
            else:
                print("\nNo items were extracted from the receipts.")
        else:
            print("No images selected.")
    except Exception as e:
        error_msg = f"An error occurred: {str(e)}"
        print(f"\n{error_msg}")
        show_error(error_msg)

if __name__ == '__main__':
    main()
