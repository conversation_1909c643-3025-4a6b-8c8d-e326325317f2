Column1,Column2,Column3,Column4,Column5,Column6,Column7,Column8,Column9
Category,Config Type,Module,Parameter,Type,Default,Required,Description,Source File
,,,,,,,,
# CURRENT ESSENTIAL CONFIGS,,,,,,,,
,,,,,,,,
# Environment (Just dev vs prod basics),,,,,,,,
env,init,app,env.mode,str,development,yes,Environment mode (dev/prod),config/env.py
env,init,app,env.debug,bool,FALSE,no,Show debug information,config/env.py
,,,,,,,,
# Core Paths (Essential file locations),,,,,,,,
sys,init,app,paths.project_root,Path,.,yes,Project root directory,core/config/paths.py
sys,init,app,paths.logs,Path,PROJECT_ROOT/logs,yes,Log files directory,core/config/paths.py
sys,init,app,paths.config,Path,PROJECT_ROOT/configs,yes,Config files directory,core/config/paths.py
sys,init,app,paths.resources,Path,PROJECT_ROOT/resources,yes,Resources directory,core/config/paths.py
,,,,,,,,
# Logging (Just the basics),,,,,,,,
log,runtime,app,log.show_info,bool,TRUE,no,Show info messages,core/logger.py
log,runtime,app,log.show_warnings,bool,TRUE,no,Show warning messages,core/logger.py
,,,,,,,,
# Update Data Module (Only what's needed now),,,,,,,,
sys,init,update_data,paths.master,Path,~/flatmate/master,yes,Master CSV location,modules/update_data/config/ud_config.py
sys,init,update_data,paths.backup,Path,~/flatmate/backup,yes,Backup directory,modules/update_data/config/ud_config.py
const,init,update_data,formats.date_format,str,%d/%m/%Y,yes,Bank date format,modules/update_data/utils/formatting/formats/bank_format.py