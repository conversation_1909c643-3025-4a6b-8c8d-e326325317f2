"""
Centralized column name mapping service.

This service provides the elegant solution for db_name vs display_name mapping
throughout the application. It uses StandardColumns as the canonical source
and provides consistent fallback strategies for unknown columns.
"""

import pandas as pd
from typing import Dict, List, Optional, Tuple
from .fm_standard_columns import StandardColumns


class ColumnNameService:
    """
    Centralized column name mapping service for the entire application.
    
    This service eliminates the need for scattered mapping logic by providing
    a single, consistent way to handle column name conversions between
    database names and display names.
    """
    
    @staticmethod
    def get_display_mapping(df_columns: List[str], 
                          custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        Get complete db_name -> display_name mapping for DataFrame columns.
        
        Args:
            df_columns: List of column names from DataFrame (db_names)
            custom_mapping: Optional custom db_name -> display_name overrides
            
        Returns:
            Complete mapping: db_name -> display_name
        """
        mapping = {}
        
        # Create reverse mapping from StandardColumns (db_name -> display_name)
        standard_reverse = {col.db_name: col.value for col in StandardColumns}
        
        for db_col in df_columns:
            if custom_mapping and db_col in custom_mapping:
                # Custom override takes precedence
                mapping[db_col] = custom_mapping[db_col]
            elif db_col in standard_reverse:
                # Use StandardColumns canonical display name
                mapping[db_col] = standard_reverse[db_col]
            else:
                # Fallback for unknown columns - convert snake_case to Title Case
                mapping[db_col] = db_col.replace('_', ' ').title()
        
        return mapping
    
    @staticmethod
    def get_reverse_mapping(df_columns: List[str], 
                          custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        Get complete display_name -> db_name mapping for DataFrame columns.
        
        Args:
            df_columns: List of column names from DataFrame (db_names)
            custom_mapping: Optional custom db_name -> display_name overrides
            
        Returns:
            Complete reverse mapping: display_name -> db_name
        """
        display_mapping = ColumnNameService.get_display_mapping(df_columns, custom_mapping)
        return {display_name: db_name for db_name, display_name in display_mapping.items()}
    
    @staticmethod
    def apply_display_names(df: pd.DataFrame, 
                          custom_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """
        Apply display names to DataFrame columns.
        
        Args:
            df: DataFrame with db_names as columns
            custom_mapping: Optional custom db_name -> display_name overrides
            
        Returns:
            New DataFrame with display names as columns
        """
        if df.empty:
            return df.copy()
            
        display_mapping = ColumnNameService.get_display_mapping(
            df.columns.tolist(), 
            custom_mapping
        )
        
        display_df = df.copy()
        display_df.columns = [display_mapping.get(col, col) for col in df.columns]
        return display_df
    
    @staticmethod
    def apply_db_names(df: pd.DataFrame, 
                      custom_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """
        Convert DataFrame with display names back to db_names.
        
        Args:
            df: DataFrame with display_names as columns
            custom_mapping: Optional custom db_name -> display_name overrides
            
        Returns:
            New DataFrame with db_names as columns
        """
        if df.empty:
            return df.copy()
            
        # Get reverse mapping (display_name -> db_name)
        reverse_mapping = ColumnNameService.get_reverse_mapping(
            # We need to reverse-engineer the db_names from display_names
            # This is a bit tricky, so we'll use StandardColumns as reference
            [col.db_name for col in StandardColumns] + 
            [col.lower().replace(' ', '_') for col in df.columns if col not in [sc.value for sc in StandardColumns]],
            custom_mapping
        )
        
        db_df = df.copy()
        db_df.columns = [reverse_mapping.get(col, col.lower().replace(' ', '_')) for col in df.columns]
        return db_df
    
    @staticmethod
    def get_standard_column_widths() -> Dict[str, int]:
        """
        Get standard column widths for consistent UI display.
        
        Returns:
            Dictionary mapping display names to character widths
        """
        return StandardColumns.get_standard_column_widths()
    
    @staticmethod
    def convert_transactions_to_dataframe(transactions: List, 
                                        ensure_columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Convert Transaction objects to DataFrame with proper db_name columns.
        
        This replaces the complex transaction_utils.transactions_to_dataframe function
        with a cleaner, more maintainable approach.
        
        Args:
            transactions: List of Transaction objects from database
            ensure_columns: Optional list of db_name columns to ensure exist (with empty values)
            
        Returns:
            DataFrame with db_name columns ready for display conversion
        """
        if not transactions:
            return pd.DataFrame()
        
        # Convert transactions to list of dictionaries using StandardColumns mapping
        data = []
        for transaction in transactions:
            trans_dict = {}
            
            # Map all StandardColumns fields
            for std_col in StandardColumns:
                db_name = std_col.db_name
                
                # Handle special field name mappings
                if db_name == 'details' and hasattr(transaction, 'description'):
                    trans_dict[db_name] = getattr(transaction, 'description', '')
                elif db_name == 'account' and hasattr(transaction, 'account_number'):
                    trans_dict[db_name] = getattr(transaction, 'account_number', '')
                elif db_name == 'id' and hasattr(transaction, 'transaction_id'):
                    trans_dict[db_name] = getattr(transaction, 'transaction_id', '')
                elif hasattr(transaction, db_name):
                    trans_dict[db_name] = getattr(transaction, db_name, '')
                else:
                    # Set empty value for missing fields
                    trans_dict[db_name] = ''
            
            data.append(trans_dict)
        
        # Create DataFrame
        df = pd.DataFrame(data)
        
        # Ensure additional columns exist if requested
        if ensure_columns:
            for col in ensure_columns:
                if col not in df.columns:
                    df[col] = ""
        
        return df


# Convenience functions for backward compatibility
def get_column_display_mapping(df_columns: List[str], 
                             custom_mapping: Optional[Dict[str, str]] = None) -> Dict[str, str]:
    """Convenience function for getting display mapping."""
    return ColumnNameService.get_display_mapping(df_columns, custom_mapping)


def apply_display_names_to_dataframe(df: pd.DataFrame, 
                                   custom_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
    """Convenience function for applying display names."""
    return ColumnNameService.apply_display_names(df, custom_mapping)
