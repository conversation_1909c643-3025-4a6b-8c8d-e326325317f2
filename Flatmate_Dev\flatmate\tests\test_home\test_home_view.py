"""
Test Home View Module.
Used to test the new main window and base module view functionality.
"""

from PySide6.QtWidgets import (
    QLabel, QPushButton, QVBoxLayout, QHBoxLayout,
    QFrame, QWidget, QLineEdit
)
from PySide6.QtCore import Signal

from ..base.base_module_view import BaseModuleView

class TestHomeView(BaseModuleView):
    """Test implementation of a module view."""
    
    # Signals for presenter communication
    button_clicked = Signal(str)
    text_changed = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.presenter = None
    
    def setup_ui(self):
        """Initial UI setup - not much needed here as panels are set up separately."""
        pass
    
    def setup_left_panel(self, layout):
        """Set up the left panel with test buttons."""
        # Add some test buttons
        buttons = [
            "Toggle Right Panel",
            "Update Center Text",
            "Clear Right Panel",
            "Test Action 1",
            "Test Action 2"
        ]
        
        for button_text in buttons:
            btn = QPushButton(button_text)
            btn.clicked.connect(lambda checked, text=button_text: self.button_clicked.emit(text))
            layout.addWidget(btn)
        
        # Add stretcher at the bottom
        layout.addStretch()
    
    def setup_center_panel(self, layout):
        """Set up the center panel with test content."""
        # Create a container for center content
        center_container = QFrame()
        center_layout = QVBoxLayout(center_container)
        
        # Add a title
        title = QLabel("Test Home Module")
        title.setObjectName("title-label")
        center_layout.addWidget(title)
        
        # Add some test content
        content = QLabel("This is a test module to verify the new main window and base module view functionality.")
        content.setWordWrap(True)
        center_layout.addWidget(content)
        
        # Add an input field
        self.input_field = QLineEdit()
        self.input_field.setPlaceholderText("Type something to update right panel...")
        self.input_field.textChanged.connect(self.text_changed.emit)
        center_layout.addWidget(self.input_field)
        
        # Add stretcher
        center_layout.addStretch()
        
        # Add to main layout
        layout.addWidget(center_container)
    
    def setup_right_panel(self, layout):
        """Set up the right panel with test content."""
        # Add a status label
        self.status_label = QLabel("Right Panel Content")
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)
        
        # Add some test widgets
        test_widget = QFrame()
        test_layout = QVBoxLayout(test_widget)
        
        # Add some sample content
        for i in range(3):
            label = QLabel(f"Test Content {i+1}")
            test_layout.addWidget(label)
        
        layout.addWidget(test_widget)
        layout.addStretch()
    
    def update_status(self, text):
        """Update the status label in the right panel."""
        if hasattr(self, 'status_label'):
            self.status_label.setText(text)
    
    def disconnect_signals(self):
        """Clean up any connected signals."""
        try:
            self.button_clicked.disconnect()
            self.text_changed.disconnect()
        except:
            pass  # Signals might not be connected
