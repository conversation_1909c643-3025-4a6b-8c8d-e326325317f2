# Task List for Flatmate App Improvements

Based on feedback and debug output, here's a prioritized task list for improving the application:

## High Priority (Core Functionality)

1. **✅ Fix Column Names Issue**
   - Fixed the issue where column names were showing as enum objects (e.g., "FmColumnFormat.ACCOUNT") instead of their string values ("ACCOUNT")
   - Added better error handling for column count mismatches

2. **✅ Fix Account Number Pattern Matching**
   - Updated pattern matching to use `re.search()` instead of the `in` operator for regex patterns
   - Added detailed logging to track pattern matching attempts

3. **Improve Column Ordering**
   - Review the current column ordering logic
   - Consider a more intuitive default order (e.g., Date, Details, Amount, Balance, Account)
   - Add configuration option for preferred column order

4. **Add Date/Time Stamp to Master Output**
   - Implement date/time stamping for FM_Master output files
   - Format options: YYYY-MM-DD_HHMMSS or YYYY-MM-DD_n (if multiple on same day)
   - Add this to the file naming convention

5. **Add Source File Type Column**
   - Add a column indicating the source file type (e.g., "Kiwibank Basic CSV")
   - Populate this column for all rows with the appropriate handler type
   - Ensure this is preserved when merging multiple files

6. **Implement FM_Master Tagging System**
   - Add an FM_Metadata column or section to identify FM_Master files
   - Include metadata such as: date added, FM version number, file type
   - Use this for validation when processing files

## Medium Priority (UI/UX Improvements)

7. **Improve File Display Widget**
   - Make file paths visible in the display
   - Improve text contrast (current gray is too dark)
   - Show format information more clearly

8. **Update Button States**
   - Change "Cancel" button to "Done" after processing
   - Add option to open the master file after processing

9. **Status Bar Enhancements**
   - Add hints in the status bar (e.g., "All applicable files in folder will be selected")
   - Show processing status and results

## Lower Priority (Development Features)

10. **Implement Debug/Dev Mode**
   - Add configuration option for debug/dev mode
   - When enabled, show more detailed logs and preserve test files
   - Add UI indicator when in debug mode

11. **Refine File Handling**
   - Review the originals folder structure (timestamped backup folders vs. single folder)
   - Implement file hash/sample check to avoid duplicates
   - Add option to not delete files in dev mode

12. **Add More Test Handlers**
    - Create specific handlers for different bank formats
    - Improve test file detection and processing




My NOTES:
Deleted AI's test file handler reinstated last good kiwibank basic handler.
Unrecognised files should for now at least 
be left alone. (We may move them to an unrecognised folder) 
Deleted testfile handler

@_base_statement_handler.py#L254-340  this looks like the procedural logic for the base model formatting... I think we need to take a closer look at this, we are stripping the metadata here... the meta data contains the acc number in some instances... 

Examine the possible reduncay or merits in involved having both process trackers and lists of processed files.unrecognised files etc

