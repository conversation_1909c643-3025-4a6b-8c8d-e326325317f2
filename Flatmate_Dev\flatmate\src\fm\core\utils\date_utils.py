"""
Date parsing and standardization utilities.
Provides functions for handling various date formats and standardizing them
for use throughout the application.
"""
from datetime import datetime
from typing import Optional, Union, List
import re

# List of common date formats to try, in order of preference
# UK formats first (day-month-year)
DATE_FORMATS = [
    '%d/%m/%Y',  # 31/12/2023
    '%d-%m-%Y',  # 31-12-2023
    '%d %b %Y',  # 31 Dec 2023
    '%d-%b-%Y',  # 31-Dec-2023
    '%d/%m/%y',  # 31/12/23
    '%d-%m-%y',  # 31-12-23
    '%d %b %y',  # 31 Dec 23
    '%d-%b-%y',  # 31-Dec-23
    '%d.%m.%Y',  # 31.12.2023
    '%d.%m.%y',  # 31.12.23
    # US formats (month-day-year) as fallback
    '%m/%d/%Y',  # 12/31/2023
    '%m-%d-%Y',  # 12-31-2023
    '%b %d %Y',  # Dec 31 2023
    '%b-%d-%Y',  # Dec-31-2023
    '%m/%d/%y',  # 12/31/23
    '%m-%d-%y',  # 12-31-23
    '%b %d %y',  # Dec 31 23
    '%b-%d-%y',  # Dec-31-23
    # ISO formats
    '%Y-%m-%d',  # 2023-12-31
    '%Y/%m/%d',  # 2023/12/31
    '%Y.%m.%d',  # 2023.12.31
]

def standardize_date(
    date_input: Union[str, datetime, None], 
    output_format: str = '%Y-%m-%d'
) -> Optional[str]:
    """
    Convert a date string or datetime object to a standardized format.
    
    Args:
        date_input: Date string in any common format or datetime object
        output_format: Format to return (default ISO YYYY-MM-DD)
        
    Returns:
        Standardized date string or None if unparseable
    """
    if date_input is None:
        return None
        
    # If already a datetime, just format it
    if isinstance(date_input, datetime):
        return date_input.strftime(output_format)
    
    # Clean the input string
    date_str = str(date_input).strip()
    
    # Try to parse with each format
    for date_format in DATE_FORMATS:
        try:
            date_obj = datetime.strptime(date_str, date_format)
            return date_obj.strftime(output_format)
        except ValueError:
            continue
    
    # Try some special case handling
    return _handle_special_cases(date_str, output_format)

def _handle_special_cases(date_str: str, output_format: str) -> Optional[str]:
    """
    Handle special date formats that don't match standard patterns.
    
    Args:
        date_str: Date string to parse
        output_format: Format to return
        
    Returns:
        Standardized date string or None if unparseable
    """
    # Handle dates with text month and possible ordinal indicators (1st, 2nd, 3rd, etc.)
    # Example: "1st Jan 2023" or "January 1st, 2023"
    month_pattern = r'(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)'
    day_pattern = r'\d{1,2}(?:st|nd|rd|th)?'
    year_pattern = r'\d{2,4}'
    
    # Pattern 1: Day Month Year (e.g., "1st Jan 2023")
    pattern1 = re.compile(f'({day_pattern})\\s*({month_pattern})\\s*({year_pattern})', re.IGNORECASE)
    match = pattern1.search(date_str)
    if match:
        day, month, year = match.groups()
        # Extract just the digits from day
        day_match = re.search(r'\d+', day)
        if not day_match:
            return None
        day = day_match.group()
        # Convert month name to number (1-12)
        month_abbr = month[:3].capitalize()
        month_num = {
            'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
            'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
        }.get(month_abbr)
        
        if month_num and 1 <= int(day) <= 31:
            # Handle 2-digit years
            if len(year) == 2:
                year = '20' + year if int(year) < 50 else '19' + year
            
            try:
                date_obj = datetime(int(year), month_num, int(day))
                return date_obj.strftime(output_format)
            except ValueError:
                pass
    
    # Pattern 2: Month Day, Year (e.g., "January 1st, 2023")
    pattern2 = re.compile(f'({month_pattern})\\s*({day_pattern})(?:,)?\\s*({year_pattern})', re.IGNORECASE)
    match = pattern2.search(date_str)
    if match:
        month, day, year = match.groups()
        # Extract just the digits from day
        day_match = re.search(r'\d+', day)
        if not day_match:
            return None
        day = day_match.group()
        # Convert month name to number (1-12)
        month_abbr = month[:3].capitalize()
        month_num = {
            'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
            'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
        }.get(month_abbr)
        
        if month_num and 1 <= int(day) <= 31:
            # Handle 2-digit years
            if len(year) == 2:
                year = '20' + year if int(year) < 50 else '19' + year
            
            try:
                date_obj = datetime(int(year), month_num, int(day))
                return date_obj.strftime(output_format)
            except ValueError:
                pass
    
    # If all else fails, return None
    return None

def convert_df_dates(
    df, 
    date_column: str, 
    output_format: str = '%Y-%m-%d'
) -> None:
    """
    Convert dates in a DataFrame column to a standardized format.
    Modifies the DataFrame in-place.
    
    Args:
        df: Pandas DataFrame
        date_column: Name of the column containing dates
        output_format: Format to convert dates to
    """
    if date_column not in df.columns:
        return
        
    # Apply the standardize_date function to each value in the column
    df[date_column] = df[date_column].apply(
        lambda x: standardize_date(x, output_format)
    )
