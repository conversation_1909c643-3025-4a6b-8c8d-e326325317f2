#!/bin/bash
# Preload Python environment for faster terminal startup

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Navigate to the flatmate directory (parent of scripts directory)
cd "$SCRIPT_DIR/.."

# Check if we're in the right place
if [ -d "flatmate" ]; then
    cd "flatmate"
elif [ -d ".venv_fm313" ]; then
    # We're already in the flatmate directory
    echo "Already in flatmate directory"
else
    echo "Error: Could not find flatmate directory or virtual environment"
    echo "Current directory: $(pwd)"
    exit 1
fi

# Check if virtual environment exists
if [ -f ".venv_fm313/Scripts/activate" ]; then
    source .venv_fm313/Scripts/activate
    echo "Virtual environment activated successfully"
else
    echo "Error: Virtual environment not found at $(pwd)/.venv_fm313"
    echo "Available directories:"
    ls -la
    exit 1
fi

# Print success message
echo "Environment preloaded successfully!"

# Optionally preload common modules
# python -c "
# import sys
# import os
# import pandas
# import yaml
# import PySide6
# print('Python modules imported successfully!')
# "
