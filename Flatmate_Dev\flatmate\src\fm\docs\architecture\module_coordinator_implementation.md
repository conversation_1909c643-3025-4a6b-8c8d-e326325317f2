# ModuleCoordinator Implementation

## Overview

This document provides detailed documentation of the ModuleCoordinator implementation in the FlatMate application. The ModuleCoordinator is a central component that manages module lifecycle, transitions between modules, and coordinates the application's navigation flow.

## Location and Dependencies

The ModuleCoordinator is implemented in:
```
/flatmate/src/fm/module_coordinator.py
```

Key dependencies:
- MainWindow (for UI integration)
- Module presenters (HomePresenter, UpdateDataPresenter, etc.)
- Configuration system (for storing recent modules)

## Core Responsibilities

The ModuleCoordinator has several key responsibilities:

1. **Module Lifecycle Management**
   - Creating module instances via factories
   - Initializing modules with required parameters
   - Cleaning up modules when they're no longer active

2. **Navigation Control**
   - Managing transitions between modules
   - Maintaining navigation history
   - Updating the navigation UI to reflect the active module

3. **Dependency Injection**
   - Providing modules with access to the main window
   - Passing parameters between modules during transitions

4. **State Management**
   - Tracking the currently active module
   - Storing recent module history

## Key Methods

### `__init__(self, main_window)`
- Initializes the coordinator with a reference to the main window
- Sets up empty module factories dictionary
- Loads recent modules from configuration 
### ?  what purpose does tracking recent modules serve?

### `initialize_modules(self)`
- Registers module factories for all available modules
- Each factory is a lambda function that creates a presenter instance
### ? how does it now know about the available modules?

### `start(self)`
- Entry point for starting the application
- Transitions to the home module by default

### `transition_to(self, module_name, **params)`
- Core method for switching between modules
- Cleans up the current module if one exists
- Creates and initializes the new module
- Updates the navigation UI to reflect the change 
### ? not sure about this, would prefer nav pane handle this module co-ordinator should publish and event, or possibly more robust, base presenter should handle this.which is more logical,easier to implement and maintain?
- Records the module in recent history

### `_connect_module_transitions(self, module)`
- Internal method to connect a module's request_transition method
- Uses functools.partial to bind the transition_to method to the module
### ? I dont understand this functools.partial

## Usage Example

The ModuleCoordinator is typically created in main.py and passed to the MainWindow:

```python
# Create main window
main_window = MainWindow()

# Create and initialize module coordinator
coordinator = ModuleCoordinator(main_window)
coordinator.initialize_modules()

# Set coordinator in main window
main_window.set_module_manager(coordinator)

# Start the application
coordinator.start()
```

## Module Transition Flow

When a transition occurs:

1. The current module's `cleanup()` method is called if it exists
2. The current module reference is cleared
3. A new module is created using its factory function
4. The module's transitions are connected to the coordinator
5. The module's `initialize()` method is called with any parameters
6. The navigation UI is updated to highlight the current module
7. The module is added to recent history

## Integration with Navigation UI

The ModuleCoordinator updates the navigation UI when modules change:

```python
if hasattr(self.main_window, 'nav_pane'):
    self.main_window.nav_pane.highlight_item(module_name)
```

This ensures the navigation pane accurately reflects the current module.
### ? poss use event, have nav_pane handle this

## Module Factory Pattern

Modules are created using a factory pattern:

```python
self.module_factories = {
    'home': lambda: HomePresenter(self.main_window),
    'update_data': lambda: UpdateDataPresenter(self.main_window)
}
```

This approach:
- Defers module creation until needed
- Provides a consistent way to create modules
- Allows for easy addition of new modules

## Architectural Considerations

### Advantages of the Current Implementation

1. **Clean Separation of Concerns**
   - Modules don't need to know about each other
   - Navigation logic is centralized
   - UI updates are handled consistently

2. **Flexible Module Management**
   - Modules can be added or removed easily
   - Module lifecycle is consistently managed
   - Parameters can be passed between modules

3. **Maintainable Navigation**
   - Single point for handling module transitions
   - Consistent cleanup of resources
   - Centralized history tracking

### Potential Improvements

1. **State Persistence**
   - Consider implementing state persistence between module transitions
   - Options include coordinator-managed state or an external state service

2. **Async Module Loading**
   - For larger modules, consider async loading to improve performance
   - Could show loading indicators during transitions
   - ? we should look into caching or pre loading... we want smooth transitions.
3. **Enhanced Navigation History**
   - Implement back/forward navigation
   - Allow for deeper history tracking

## Conclusion

The ModuleCoordinator is a central architectural component that provides clean separation between modules while managing navigation and lifecycle. Understanding its implementation is key to working with the modular architecture of the FlatMate application.
