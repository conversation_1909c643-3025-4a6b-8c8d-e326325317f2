# Flatmate App Pending Tasks

## UI Issues

### Styling Issues
- [ ] Fix subheading label colors in update_data module (black instead of calm-white)
- [ ] Create a programmatic styling solution for labels to avoid QSS variable limitations
- [ ] Standardize label styling across the application

### Component Architecture
- [ ] Refactor utilities pane to use pub/sub pattern instead of direct signal connections
- [ ] Move utility event handling out of MainWindow
- [ ] Consider integrating settings icon with existing component rather than separate pane

## Architecture Improvements

### MainWindow Refactoring
- [ ] Remove granular implementation details from MainWindow
- [ ] Create proper interfaces between components
- [ ] Implement proper event system for component communication

### Module Coordinator
- [ ] Evaluate necessity and complexity of ModuleCoordinator
- [ ] Improve module transitions and navigation control
- [ ] Consider simplifying if it leads to clearer architecture

## Documentation

### Architecture Documentation
- [ ] Complete ModuleCoordinator implementation documentation
- [ ] Document event system and component communication patterns
- [ ] Create diagrams for architectural overview

## Technical Debt

### Stylesheet System
- [ ] Fix CSS variable handling in Qt stylesheets
- [ ] Consider alternatives to QSS for styling
- [ ] Create consistent styling approach that works reliably

### Code Organization
- [ ] Organize components into logical categories
- [ ] Create proper interfaces between system components
- [ ] Avoid direct widget access between components

## How to Use This Document

- Add new tasks with `- [ ]` for uncompleted tasks
- Mark completed tasks with `- [x]`
- Add new categories as needed with `## Category Name`
- Add subcategories with `### Subcategory Name`
- Review and update regularly
