"""
Test script for the InfoBar implementation.
Run this script to verify that the InfoBar is working correctly.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (
    QApplication,
    QFrame,
    QMainWindow,
    QPushButton,
    QVBoxLayout,
    QWidget,
)
from src.fm.core.services.event_bus import Events, global_event_bus
from src.fm.gui.widgets.info_bar import InfoBar


class PanelManager(QFrame):
    """Simulates a panel manager like in the real application."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Shape.StyledPanel)
        self.setStyleSheet("background-color: #f0f0f0;")

        # Main layout for the panel manager
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 10, 10, 10)

        # Content area
        content = QWidget()
        content_layout = QVBoxLayout(content)

        # Add some buttons to the content
        self.show_button = QPushButton("Show Message")
        self.error_button = QPushButton("Show Error")
        self.clear_button = QPushButton("Clear Message")

        content_layout.addWidget(self.show_button)
        content_layout.addWidget(self.error_button)
        content_layout.addWidget(self.clear_button)
        content_layout.addStretch(1)  # Push buttons to the top

        # Create InfoBar and add it to the panel manager's layout
        self.info_widget = InfoBar()

        # Add content and info widget to the main layout
        self.main_layout.addWidget(content, 1)  # Content gets all available space
        self.main_layout.addWidget(self.info_widget)  # InfoBar at the bottom


class TestWindow(QMainWindow):
    """Test window for InfoBar."""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("InfoBar Test - Panel Manager Integration")
        self.resize(800, 600)

        # Create central widget
        central = QWidget()
        self.setCentralWidget(central)
        layout = QVBoxLayout(central)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create panel manager (simulates the center panel in the real app)
        self.panel_manager = PanelManager()
        layout.addWidget(self.panel_manager)

        # Connect button signals
        self.panel_manager.show_button.clicked.connect(self.show_message)
        self.panel_manager.error_button.clicked.connect(self.show_error)
        self.panel_manager.clear_button.clicked.connect(self.clear_message)

        # Subscribe to events
        self.event_bus = global_event_bus
        self.event_bus.subscribe(Events.INFO_MESSAGE, self.handle_info_message)
        self.event_bus.subscribe(Events.INFO_CLEAR, self.handle_info_clear)

    def handle_info_message(self, message):
        """Handle info message events."""
        if hasattr(self.panel_manager, "info_widget"):
            self.panel_manager.info_widget.set_message(message)

    def handle_info_clear(self, _=None):
        """Handle info clear events."""
        if hasattr(self.panel_manager, "info_widget"):
            self.panel_manager.info_widget.clear()

    def show_message(self):
        """Publish a normal message."""
        self.event_bus.publish(Events.INFO_MESSAGE, "This is a test message")

    def show_error(self):
        """Publish an error message."""
        self.event_bus.publish(Events.INFO_MESSAGE, "Error: Something went wrong")

    def clear_message(self):
        """Clear the message."""
        self.event_bus.publish(Events.INFO_CLEAR)

    def closeEvent(self, event):
        """Clean up event subscriptions when closing."""
        self.event_bus.unsubscribe(Events.INFO_MESSAGE, self.handle_info_message)
        self.event_bus.unsubscribe(Events.INFO_CLEAR, self.handle_info_clear)
        super().closeEvent(event)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec())
