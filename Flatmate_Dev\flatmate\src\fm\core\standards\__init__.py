# fm/core/standards
"""
Core standards module for Flatmate application.

This module provides the canonical column system and related utilities.
StandardColumns is the primary/canonical system as specified by the user.
"""

# Primary/Canonical column system (as specified by user)
from .fm_standard_columns import StandardColumns, FmColumnFormat

# Simple column manager (follows StandardColumns architecture)
from .simple_column_manager import SimpleColumnManager, get_simple_column_manager

# NEW: Elegant column name service (the future!)
from .column_name_service import ColumnNameService

# Public API - expose both current and elegant systems
__all__ = [
    'StandardColumns',        # Canonical column definitions
    'FmColumnFormat',        # Backward compatibility alias
    'SimpleColumnManager',   # Correct column manager implementation (current)
    'get_simple_column_manager',  # Factory function (current)
    'ColumnNameService',      # Elegant column service (future)
]