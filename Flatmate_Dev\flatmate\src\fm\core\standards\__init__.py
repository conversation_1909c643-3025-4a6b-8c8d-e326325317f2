# fm/core/standards
"""
Core standards module for Flatmate application.

This module provides the canonical column system and related utilities.
StandardColumns is the primary/canonical system as specified by the user.
"""

# Primary/Canonical column system (as specified by user)
from .fm_standard_columns import StandardColumns, FmColumnFormat

# Simple column manager (follows StandardColumns architecture)
from .simple_column_manager import SimpleColumnManager, get_simple_column_manager

# Public API - only expose canonical systems
__all__ = [
    'StandardColumns',        # Canonical column definitions
    'FmColumnFormat',        # Backward compatibility alias
    'SimpleColumnManager',   # Correct column manager implementation
    'get_simple_column_manager',  # Factory function
]