"""Test the data service with tags column and filters."""
import os
import tempfile
import unittest
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path

import pandas as pd

# Fix imports to match the project structure
from fm.database_service.service import DataService
from fm.database_service.repository.sqlite_repository import SQLiteTransactionRepository
from fm.database_service.repository.transaction_repository import Transaction
from fm.database_service.migrations.update_schema_consistency import run_migration

class TestDataServiceTags(unittest.TestCase):
    """Test cases for the data service with tags support."""
    
    def setUp(self):
        """Create a test database with sample data."""
        # Create a temporary file for the test database
        self.temp_db_fd, self.temp_db_path = tempfile.mkstemp(suffix='.db')
        self.db_path = Path(self.temp_db_path)
        
        # Create a connection to the database
        self.conn = sqlite3.connect(self.temp_db_path)
        self.cursor = self.conn.cursor()
        
        # Create transactions table
        self.cursor.execute('''
            CREATE TABLE transactions (
                id INTEGER PRIMARY KEY,
                date TEXT,
                description TEXT,
                amount REAL,
                account_number TEXT,
                is_deleted INTEGER DEFAULT 0
            )
        ''')
        
        # Run the migration to add the tags column
        run_migration(self.db_path)
        
        # Insert test data with various dates, accounts, and tags
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        last_week = today - timedelta(days=7)
        
        test_data = [
            (today.isoformat(), 'Grocery Store', -50.0, '123456', 'food'),
            (yesterday.isoformat(), 'Gas Station', -30.0, '123456', 'transport'),
            (yesterday.isoformat(), 'Restaurant', -25.0, '789012', 'food'),
            (last_week.isoformat(), 'Salary', 1000.0, '123456', 'income'),
            (last_week.isoformat(), 'Internet Bill', -60.0, '789012', 'utilities')
        ]
        
        for date, desc, amount, account, tag in test_data:
            self.cursor.execute('''
                INSERT INTO transactions 
                (date, description, amount, account_number, tags) 
                VALUES (?, ?, ?, ?, ?)
            ''', (date, desc, amount, account, tag))
        
        self.conn.commit()
        
        # Create the data service
        self.repository = SQLiteTransactionRepository(self.db_path)
        self.data_service = DataService(self.repository)
    
    def tearDown(self):
        """Clean up the test database."""
        self.conn.close()
        os.close(self.temp_db_fd)
        os.unlink(self.temp_db_path)
    
    def test_get_transactions_with_tags(self):
        """Test retrieving transactions with tags."""
        # Get all transactions
        all_transactions = self.data_service.get_transactions()
        
        # Check that tags are included
        self.assertTrue(all(hasattr(t, 'tags') for t in all_transactions))
        
        # Check specific tags
        food_transactions = self.data_service.get_transactions({'tags': 'food'})
        self.assertEqual(len(food_transactions), 2)
        self.assertTrue(all(t.tags == 'food' for t in food_transactions))
    
    def test_get_transactions_with_date_filter(self):
        """Test retrieving transactions with date filters."""
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        
        # Get today's transactions
        today_transactions = self.data_service.get_transactions({
            'start_date': today.isoformat(),
            'end_date': today.isoformat()
        })
        self.assertEqual(len(today_transactions), 1)
        self.assertEqual(today_transactions[0].description, 'Grocery Store')
        
        # Get yesterday's transactions
        yesterday_transactions = self.data_service.get_transactions({
            'start_date': yesterday.isoformat(),
            'end_date': yesterday.isoformat()
        })
        self.assertEqual(len(yesterday_transactions), 2)
    
    def test_get_transactions_with_account_filter(self):
        """Test retrieving transactions with account filter."""
        # Get transactions for account 123456
        account_transactions = self.data_service.get_transactions({
            'account_number': '123456'
        })
        self.assertEqual(len(account_transactions), 3)
        self.assertTrue(all(t.account_number == '123456' for t in account_transactions))
    
    def test_get_transactions_with_combined_filters(self):
        """Test retrieving transactions with combined filters."""
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        
        # Get food transactions from yesterday
        filtered_transactions = self.data_service.get_transactions({
            'start_date': yesterday.isoformat(),
            'end_date': yesterday.isoformat(),
            'tags': 'food'
        })
        self.assertEqual(len(filtered_transactions), 1)
        self.assertEqual(filtered_transactions[0].description, 'Restaurant')
        self.assertEqual(filtered_transactions[0].tags, 'food')

if __name__ == "__main__":
    unittest.main()

