Category,Config Type,Module,Parameter,Type,Default,Required,Description,Source File

# CATEGORIES:
# env: Environment-specific settings (dev/prod/staging)
# sys: System configuration (paths, connections, resources)
# sec: Security settings (keys, auth, permissions)
# log: Logging and monitoring configuration
# pref: User preferences and customization
# perf: Performance tuning parameters

# Environment Configuration
env,init,main,env.mode,str,development,yes,Environment mode (development/production),config/env.py
env,init,main,env.debug,bool,false,yes,Enable debug features,config/env.py
env,init,main,env.features.experimental,bool,false,no,Enable experimental features,config/env.py
env,init,update_data,env.strict_mode,bool,true,yes,Strict data validation in module,config/ud_config.py

# System Configuration
sys,init,app,sys.paths.data,Path,~/flatmate/data,yes,Main data directory,config/paths.py
sys,init,app,sys.paths.temp,Path,~/flatmate/temp,yes,Temporary file directory,config/paths.py
sys,init,app,sys.paths.backup,Path,~/flatmate/backup,yes,Backup directory,config/paths.py
sys,init,update_data,sys.paths.master,Path,~/flatmate/master,yes,Master file directory,config/ud_config.py

# Security Configuration
sec,init,app,sec.api_key_file,Path,~/.flatmate/keys,yes,API keys storage location,config/security.py
sec,init,app,sec.encryption_key_file,Path,~/.flatmate/encryption,yes,Encryption keys location,config/security.py
sec,init,app,sec.auth_mode,str,local,yes,Authentication mode (local/ldap),config/security.py

# Logging Configuration
log,runtime,app,log.level,str,INFO,yes,Application log level,config/logging.py
log,runtime,app,log.format,str,[%%(levelname)s] %%(message)s,yes,Log message format,config/logging.py
log,runtime,app,log.file_rotation,int,7,no,Days to keep log files,config/logging.py
log,runtime,update_data,log.show_warnings,bool,true,no,Show data validation warnings,config/ud_config.py

# User Preferences
pref,runtime,app,pref.ui.theme,str,light,no,UI theme (light/dark),config/preferences.py
pref,runtime,app,pref.ui.font_size,int,12,no,UI font size,config/preferences.py
pref,runtime,app,pref.locale,str,en_US,no,User locale setting,config/preferences.py
pref,runtime,update_data,pref.default_source,str,folder,no,Default data source type,config/ud_config.py

# Performance Settings
perf,init,app,perf.cache_size,int,1000,no,Maximum cache entries,config/performance.py
perf,init,app,perf.batch_size,int,500,no,Default batch processing size,config/performance.py
perf,init,app,perf.max_memory,int,1073741824,no,Maximum memory usage (1GB),config/performance.py
perf,init,update_data,perf.chunk_size,int,8192,no,File processing chunk size,config/ud_config.py
