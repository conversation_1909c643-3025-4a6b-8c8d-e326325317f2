# PySide6 Qt Documentation

This directory contains documentation for PySide6 Qt-specific implementations and solutions used in the Flatmate application.

## Contents

- [Custom Title Bar Solutions](./custom-title-bar-solutions.md) - Comprehensive guide to frameless window implementations
- [Window State Management](./window-state-management.md) - Best practices for window maximize/restore functionality
- [Qt Libraries Integration](./qt-libraries-integration.md) - Third-party Qt libraries and their integration

## Overview

The Flatmate application uses PySide6 for its GUI framework. This documentation covers:

1. **Custom Window Management** - Frameless windows with custom title bars
2. **State Synchronization** - Handling window state changes reliably
3. **Cross-Platform Compatibility** - Ensuring consistent behavior across Windows, Linux, and macOS
4. **Performance Optimization** - Best practices for Qt widget performance

## Quick Reference

### Current Implementation Status
- ✅ Custom title bar with minimize, maximize, close buttons
- ⚠️ Window state synchronization issues (being resolved)
- 🔄 Migration to PyQt-Frameless-Window library in progress

### Key Files
- `fm/gui/main_window.py` - Main application window
- `fm/gui/_main_window_components/title_bar/` - Custom title bar components
- `fm/gui/_main_window_components/title_bar/components/window_controls.py` - Window control buttons

## Migration Notes

We are currently migrating from a custom frameless window implementation to the PyQt-Frameless-Window library to resolve window state synchronization issues and improve reliability.

See [Custom Title Bar Solutions](./custom-title-bar-solutions.md) for detailed migration guidance.
