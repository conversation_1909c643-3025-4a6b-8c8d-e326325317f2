"""
Enhanced Table View v2 - Complete Table Solution

This is the main entry point for the enhanced table system v2. It provides a complete
table widget with filtering, column management, and export capabilities following
the app-wide widget pattern.

Features:
- configure() method for instance runtime defaults
- Dynamic methods for runtime changes
- Chainable API for fluent configuration
- No competing override chains
"""

from typing import Dict, List, Union, Optional
import pandas as pd

from PySide6.QtCore import Qt
from PySide6.QtGui import QAction
from PySide6.QtWidgets import QWidget, QVBoxLayout, QFrame, QMenu

from .components.table_view_core import TableViewCore
from .components.toolbar import TableViewToolbar
from .components.table_config_v2 import TableConfig
from fm.core.standards.column_name_service import ColumnNameService


class CustomTableView_v2(QWidget):
    """
    Complete table solution v2 following app-wide widget pattern.

    Features:
    - configure() method for instance runtime defaults
    - Dynamic methods for runtime changes (hide_columns, resize_column, etc.)
    - Chainable API for fluent configuration
    - No competing override chains
    - Optional toolbar

    Usage:
        # Basic usage
        table = CustomTableView_v2()
        table.set_dataframe(df).show()

        # Configured usage
        table = CustomTableView_v2()
        table.configure(
            auto_size_columns=True,
            max_column_width=40,
            editable_columns=['tags'],
            show_toolbar=True
        ).set_dataframe(df).show()

        # Dynamic changes
        table.hide_columns(['balance']).resize_column('details', 60)
    """

    def __init__(self, parent=None):
        """Initialize with sensible defaults."""
        super().__init__(parent)

        # Configuration and state
        self._config = TableConfig()
        self._dataframe = None
        self._is_shown = False

        # UI components (will be created in _setup_ui)
        self.toolbar = None
        self.table_view = None
        self._separator = None

        # Set up UI
        self._setup_ui()

    def _setup_ui(self):
        """Set up the UI components."""
        # Main layout
        self._layout = QVBoxLayout(self)
        self._layout.setContentsMargins(0, 0, 0, 0)
        self._layout.setSpacing(2)  # Minimal spacing between toolbar and table

        # Toolbar component (initially visible based on config)
        self.toolbar = TableViewToolbar()
        self._layout.addWidget(self.toolbar)

        # Separator
        self._separator = QFrame()
        self._separator.setFrameShape(QFrame.Shape.HLine)
        self._separator.setFrameShadow(QFrame.Shadow.Sunken)
        self._layout.addWidget(self._separator)

        # Core table view (the actual table)
        self.table_view = TableViewCore()
        self._layout.addWidget(self.table_view, 1)  # Give table stretch priority

        # Connect toolbar signals to table functionality
        self._connect_signals()

        # Apply initial configuration
        self._apply_toolbar_visibility()

    # === CORE PATTERN METHODS ===

    def configure(self, **kwargs) -> 'CustomTableView_v2':
        """Configure table behavior and appearance.

        This sets the instance runtime defaults for this table.

        Args:
            **kwargs: Configuration options. See TableConfig for all available options.

        Common options:
            auto_size_columns (bool): Automatically size columns to content
            max_column_width (int): Maximum column width in characters
            column_widths (Dict[str, int]): Explicit column widths
            editable_columns (List[str]): Columns that can be edited
            default_visible_columns (List[str]): Columns to show by default
            show_toolbar (bool): Whether to show the toolbar
            column_display_mapping (Dict[str, str]): Map db_names to display_names

        Returns:
            self for method chaining

        Example:
            table.configure(
                auto_size_columns=True,
                max_column_width=40,
                editable_columns=['tags'],
                show_toolbar=True
            )
        """
        # Update config with provided kwargs
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
            else:
                raise ValueError(f"Unknown configuration option: {key}. See TableConfig for valid options.")

        # Validate configuration
        self._config.validate()

        # Apply configuration if already shown
        if self._is_shown and self._dataframe is not None:
            self._apply_configuration()

        return self

    def set_dataframe(self, df: pd.DataFrame, custom_column_names: Optional[Dict[str, str]] = None) -> 'CustomTableView_v2':
        """Set the table data from a pandas DataFrame with automatic column name handling.

        Args:
            df: DataFrame with db_names as columns (from database)
            custom_column_names: Optional custom db_name -> display_name mapping

        Returns:
            self for method chaining

        The table view handles all column name conversion internally using ColumnNameService.
        """
        if df.empty:
            self._dataframe = df
            return self

        # Store original DataFrame with db_names for internal use
        self._dataframe_db = df

        # Get display mapping using ColumnNameService
        display_mapping = ColumnNameService.get_display_mapping(
            df.columns.tolist(),
            custom_column_names
        )

        # Create display DataFrame for UI
        self._dataframe = ColumnNameService.apply_display_names(df, custom_column_names)

        # Store mapping for internal use and configuration
        self._column_mapping = display_mapping
        self._config.column_display_mapping = display_mapping

        # Apply configuration if already shown
        if self._is_shown:
            self._apply_configuration()

        return self

    def show(self) -> 'CustomTableView_v2':
        """Show the table and apply all configuration.

        This triggers the display and applies all pending configuration.

        Returns:
            self for method chaining
        """
        self._is_shown = True

        if self._dataframe is not None:
            self._apply_configuration()

        super().show()
        return self

    def hide(self) -> 'CustomTableView_v2':
        """Hide the table.

        Returns:
            self for method chaining
        """
        self._is_shown = False
        super().hide()
        return self

    # === DYNAMIC RUNTIME METHODS ===

    def set_visible_columns(self, columns: List[str]) -> 'CustomTableView_v2':
        """Set which columns are visible.

        Args:
            columns: List of column names to make visible

        Returns:
            self for method chaining
        """
        self._config.default_visible_columns = columns.copy()
        if self._is_shown:
            self._apply_column_visibility()
        return self

    def hide_columns(self, columns: List[str]) -> 'CustomTableView_v2':
        """Hide specific columns.

        Args:
            columns: List of column names to hide

        Returns:
            self for method chaining
        """
        if self._config.default_visible_columns is None:
            # If no visible columns set, start with all columns
            if self._dataframe is not None:
                self._config.default_visible_columns = list(self._dataframe.columns)
            else:
                return self

        # Remove specified columns from visible list
        self._config.default_visible_columns = [
            col for col in self._config.default_visible_columns
            if col not in columns
        ]

        if self._is_shown:
            self._apply_column_visibility()
        return self

    def show_columns(self, columns: List[str]) -> 'CustomTableView_v2':
        """Show specific columns.

        Args:
            columns: List of column names to show

        Returns:
            self for method chaining
        """
        if self._config.default_visible_columns is None:
            self._config.default_visible_columns = columns.copy()
        else:
            # Add columns to visible list (avoid duplicates)
            for col in columns:
                if col not in self._config.default_visible_columns:
                    self._config.default_visible_columns.append(col)

        if self._is_shown:
            self._apply_column_visibility()
        return self

    def resize_column(self, column: str, width: int) -> 'CustomTableView_v2':
        """Resize a specific column.

        Args:
            column: Column name to resize
            width: New width in characters

        Returns:
            self for method chaining
        """
        self._config.column_widths[column] = width
        if self._is_shown:
            self._apply_column_width(column, width)
        return self

    def auto_resize_columns(self) -> 'CustomTableView_v2':
        """Re-trigger auto-sizing for all columns.

        Returns:
            self for method chaining
        """
        if self._is_shown:
            self._apply_auto_sizing()
        return self

    def show_toolbar(self) -> 'CustomTableView_v2':
        """Show the toolbar.

        Returns:
            self for method chaining
        """
        self._config.show_toolbar = True
        if self._is_shown:
            self._apply_toolbar_visibility()
        return self

    def hide_toolbar(self) -> 'CustomTableView_v2':
        """Hide the toolbar.

        Returns:
            self for method chaining
        """
        self._config.show_toolbar = False
        if self._is_shown:
            self._apply_toolbar_visibility()
        return self

    def _connect_signals(self):
        """Connect toolbar signals to table functionality."""
        # Connect filter signals
        self.toolbar.filter_applied.connect(self._on_filter_applied)
        self.toolbar.filters_cleared.connect(self._on_filters_cleared)

        # Connect action signals
        self.toolbar.column_visibility_requested.connect(self._show_column_visibility)
        self.toolbar.csv_export_requested.connect(self._export_csv)
        self.toolbar.excel_export_requested.connect(self._export_excel)

    # === INTERNAL CONFIGURATION APPLICATION ===

    def _apply_configuration(self):
        """Apply all configuration to the UI components."""
        if self._dataframe is None:
            return

        # Set the dataframe in the table view
        self.table_view.set_dataframe(self._dataframe)

        # Apply column configuration
        self._apply_column_widths()
        self._apply_column_visibility()
        self._apply_editable_columns()

        # Update toolbar
        self._update_toolbar()
        self._apply_toolbar_visibility()

    def _apply_column_widths(self):
        """Apply column width configuration."""
        if self._dataframe is None:
            return

        # Always apply auto-sizing with limit, even if no custom widths are set
        if self._config.auto_size_columns:
            if self._config.column_widths:
                # Use custom widths with auto-sizing for others
                self.table_view.set_column_widths(
                    self._config.column_widths,
                    max_chars=self._config.max_column_width
                )
            else:
                # Just auto-size all columns with limit
                self.table_view._auto_resize_columns_with_limit(self._config.max_column_width)
        elif self._config.column_widths:
            # Only apply custom widths without auto-sizing
            self.table_view.set_column_widths(
                self._config.column_widths,
                max_chars=self._config.max_column_width
            )

    def _apply_column_visibility(self):
        """Apply column visibility configuration."""
        if self._dataframe is None or not hasattr(self.table_view, '_model'):
            return

        all_columns = list(self._dataframe.columns)

        for col_idx, col_name in enumerate(all_columns):
            if col_idx < self.table_view._model.columnCount():
                is_visible = self._config.is_column_visible(col_name, all_columns)
                self.table_view.setColumnHidden(col_idx, not is_visible)

    def _apply_editable_columns(self):
        """Apply editable columns configuration."""
        if self._config.editable_columns:
            self.table_view.set_editable_columns(self._config.editable_columns)

    def _apply_column_width(self, column: str, width: int):
        """Apply width change to a specific column."""
        if self._dataframe is None:
            return

        # Find column index
        try:
            col_idx = list(self._dataframe.columns).index(column)
            char_width = self.table_view.fontMetrics().averageCharWidth()
            pixel_width = width * char_width
            self.table_view.horizontalHeader().resizeSection(col_idx, pixel_width)
        except ValueError:
            pass  # Column not found

    def _apply_auto_sizing(self):
        """Re-apply auto-sizing to all columns."""
        if hasattr(self.table_view, '_auto_resize_columns_with_limit'):
            self.table_view._auto_resize_columns_with_limit(self._config.max_column_width)

    def _apply_toolbar_visibility(self):
        """Apply toolbar visibility configuration."""
        if self._config.show_toolbar:
            self.toolbar.show()
            self._separator.show()
        else:
            self.toolbar.hide()
            self._separator.hide()

    def _update_toolbar(self):
        """Update toolbar with current columns."""
        if self._dataframe is not None:
            columns = list(self._dataframe.columns)
            column_names = self._config.column_display_mapping
            self.toolbar.set_columns(columns, column_names)

    # === SIGNAL PROPERTIES ===

    @property
    def row_selected(self):
        """Signal emitted when a row is selected."""
        return self.table_view.row_selected

    @property
    def cell_edited(self):
        """Signal emitted when a cell is edited."""
        return self.table_view.cell_edited

    def _on_filter_applied(self, column, pattern):
        """Handle filter applied signal from toolbar."""
        self.table_view.set_column_filter(column, pattern)

    def _on_filters_cleared(self):
        """Handle filters cleared signal from toolbar."""
        self.table_view.clear_filters()

    def _show_column_visibility(self):
        """Show column visibility menu."""
        menu = QMenu(self)

        for col in range(self.table_view._model.columnCount()):
            col_name = self.table_view._model.headerData(col, Qt.Horizontal)
            action = QAction(str(col_name), self)
            action.setCheckable(True)
            action.setChecked(not self.table_view.isColumnHidden(col))
            action.triggered.connect(lambda checked, column=col:
                                    self.table_view.setColumnHidden(column, not checked))
            menu.addAction(action)

        # Show menu relative to the column group button (not far left of toolbar)
        column_group = self.toolbar.column_group
        if column_group and hasattr(column_group, 'column_visibility_button'):
            # Position menu below the column visibility button
            button_pos = column_group.column_visibility_button.mapToGlobal(
                column_group.column_visibility_button.rect().bottomLeft())
            menu.exec(button_pos)
        else:
            # Fallback to center of toolbar if button not found
            toolbar_center = self.toolbar.rect().center()
            menu.exec(self.toolbar.mapToGlobal(toolbar_center))

    def _export_csv(self):
        """Handle CSV export request."""
        self.table_view._export_data("csv")

    def _export_excel(self):
        """Handle Excel export request."""
        self.table_view._export_data("excel")

    # === BACKWARD COMPATIBILITY METHODS ===
    # These methods maintain compatibility with existing code while using the new pattern

    def get_dataframe(self) -> pd.DataFrame:
        """Get the current data as a pandas DataFrame."""
        if self.table_view:
            return self.table_view.get_dataframe()
        return pd.DataFrame()

    def set_editable_columns(self, columns: List[Union[int, str]]) -> 'CustomTableView_v2':
        """Set which columns should be editable.

        Backward compatibility method that updates configuration.

        Args:
            columns: List of column names or indices that should be editable

        Returns:
            self for method chaining
        """
        # Convert to string column names if needed
        str_columns = []
        for col in columns:
            if isinstance(col, str):
                str_columns.append(col)
            elif isinstance(col, int) and self._dataframe is not None:
                # Convert index to column name
                if 0 <= col < len(self._dataframe.columns):
                    str_columns.append(self._dataframe.columns[col])

        self._config.editable_columns = str_columns

        if self._is_shown:
            self._apply_editable_columns()

        return self

    def set_display_columns(self, columns, column_names=None) -> 'CustomTableView_v2':
        """Set which columns to display and their display names.

        Backward compatibility method that updates configuration.

        Args:
            columns: List of database column names to display
            column_names: Dictionary mapping database column names to display names

        Returns:
            self for method chaining
        """
        # Update configuration
        self._config.default_visible_columns = columns.copy() if columns else None
        if column_names:
            self._config.column_display_mapping = column_names.copy()

        # Apply if shown
        if self._is_shown:
            self._apply_column_visibility()
            self._update_toolbar()

        return self

    def set_column_widths(self, width_map: Dict[str, int]) -> 'CustomTableView_v2':
        """Set custom column widths.

        Backward compatibility method that updates configuration.

        Args:
            width_map: Dictionary mapping column names to widths (in characters)

        Returns:
            self for method chaining
        """
        # Update configuration
        self._config.column_widths.update(width_map)

        # Apply if shown
        if self._is_shown:
            self._apply_column_widths()

        return self


# Alias for backward compatibility
CustomTableView = CustomTableView_v2



