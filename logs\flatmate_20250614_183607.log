2025-06-14 18:36:07,656 - INFO - fm.core.services.logger - === FlatMate Application Starting (Event-Based Logger) ===
2025-06-14 18:36:07,658 - INFO - fm.core.services.logger - Python version: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]
2025-06-14 18:36:07,661 - INFO - fm.core.services.logger - Log file for this run: C:\Users\<USER>\.flatmate\logs\flatmate_20250614_183607.log
2025-06-14 18:36:07,663 - INFO - fm.core.services.logger - Log level: DEBUG
2025-06-14 18:36:07,665 - DEBUG - fm.main - Ensured directory exists: C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\data
2025-06-14 18:36:07,666 - DEBUG - fm.main - Ensured directory exists: C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\logs
2025-06-14 18:36:07,668 - DEBUG - fm.main - Ensured directory exists: C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\config
2025-06-14 18:36:07,669 - DEBUG - fm.main - Ensured directory exists: C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\cache
2025-06-14 18:36:07,819 - INFO - fm.main - 
=== Setting up Module Coordinator ===
2025-06-14 18:36:07,820 - INFO - fm.module_coordinator - Initializing Module Coordinator
2025-06-14 18:36:07,821 - DEBUG - fm.module_coordinator - Loaded recent modules: []
2025-06-14 18:36:07,822 - INFO - fm.module_coordinator - Initializing Module Factories
2025-06-14 18:36:07,823 - DEBUG - fm.module_coordinator - Registered module factories: ['home', 'update_data']
2025-06-14 18:36:08,023 - INFO - fm.module_coordinator - Starting Application
2025-06-14 18:36:08,033 - INFO - fm.modules.home.home_presenter - Initializing Home Presenter
2025-06-14 18:36:09,998 - DEBUG - fm.modules.home.home_presenter - Home Presenter initialization complete
2025-06-14 18:36:09,999 - INFO - fm.modules.home.home_presenter - Initializing Home Module
2025-06-14 18:36:10,000 - DEBUG - fm.modules.home.home_presenter - Connecting Home View signals
2025-06-14 18:36:10,022 - DEBUG - fm.modules.home.home_presenter - Home Module initialization complete
2025-06-14 18:36:10,024 - INFO - fm.main - 
=== Application Ready ===
2025-06-14 18:36:36,968 - INFO - fm.modules.home.home_presenter - Cleaning up Home Module
2025-06-14 18:36:36,975 - DEBUG - fm.modules.home.home_presenter - Home Module cleanup complete
2025-06-14 18:36:36,977 - INFO - fm.module_coordinator - Entering __init__
2025-06-14 18:36:37,077 - DEBUG - fm.modules.update_data.ud_presenter - Signals connected
2025-06-14 18:36:37,078 - INFO - fm.module_coordinator - Exiting __init__
2025-06-14 18:36:37,080 - INFO - fm.modules.base.base_module_view - Setting up UpdateDataView in Main Window
2025-06-14 18:36:37,081 - DEBUG - fm.modules.base.base_module_view - Setting up Left Panel
2025-06-14 18:36:37,093 - DEBUG - fm.modules.base.base_module_view - Setting up Center Panel
2025-06-14 18:36:37,122 - INFO - fm.modules.base.base_module_view - UpdateDataView setup complete
2025-06-14 18:36:37,124 - DEBUG - fm.modules.update_data.ud_presenter - ud_presenter called self.view.setup_in_main_window
2025-06-14 18:37:04,285 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_13Jun.CSV
2025-06-14 18:37:04,286 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] DataFrame shape: (731, 16)
2025-06-14 18:37:04,288 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Column names: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:04,289 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-06-14 18:37:04,311 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying to find handler for file: 38-9004-0646977-01_13Jun.CSV
2025-06-14 18:37:04,313 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] DataFrame shape: (32, 16)
2025-06-14 18:37:04,316 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Column names: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:04,319 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-06-14 18:37:04,358 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying to find handler for file: 38-9004-0646977-04_13Jun.CSV
2025-06-14 18:37:04,359 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] DataFrame shape: (1343, 16)
2025-06-14 18:37:04,360 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Column names: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:04,361 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-06-14 18:37:24,951 - INFO - fm.modules.update_data.ud_presenter - Processing started for 3 files
2025-06-14 18:37:24,975 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying to find handler for file: 38-9004-0646977-00_13Jun.CSV
2025-06-14 18:37:24,977 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] DataFrame shape: (731, 16)
2025-06-14 18:37:24,980 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Column names: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:24,982 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-06-14 18:37:24,988 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankBasicCSVHandler] Checking if file matches: 38-9004-0646977-00_13Jun.CSV
2025-06-14 18:37:24,990 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankBasicCSVHandler] Expected columns: 5, Actual: 16
2025-06-14 18:37:24,992 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankBasicCSVHandler] Column count mismatch: expected 5, got 16
2025-06-14 18:37:24,994 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-06-14 18:37:24,995 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-06-14 18:37:24,997 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking if file matches: 38-9004-0646977-00_13Jun.CSV
2025-06-14 18:37:24,999 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Expected columns: 16, Actual: 16
2025-06-14 18:37:25,002 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking column headers - Expected: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:25,006 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking column headers - Actual: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:25,008 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking account number pattern: 38-?\d{4}-?\d{7}-?\d{2,3}.*
2025-06-14 18:37:25,009 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking account number pattern '38-?\d{4}-?\d{7}-?\d{2,3}.*' in file: 38-9004-0646977-00_13Jun.CSV
2025-06-14 18:37:25,011 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking data at [0,0]: '38-9004-0646977-00'
2025-06-14 18:37:25,012 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Found account number in data
2025-06-14 18:37:25,014 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] File matches handler criteria: 38-9004-0646977-00_13Jun.CSV
2025-06-14 18:37:25,015 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-06-14 18:37:25,018 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Mapping columns: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance'] -> ['Account', 'Date', 'Details', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'Credit', 'Debit', 'Amount', 'Balance']
2025-06-14 18:37:25,021 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] DataFrame shape: (731, 16)
2025-06-14 18:37:25,085 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Sample dates before standardization: ['19-06-2023', '20-06-2023', '20-06-2023']
2025-06-14 18:37:25,087 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Standardizing dates using format: %d/%m/%y
2025-06-14 18:37:25,184 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Date conversion success rate: 100.0% (731/731)
2025-06-14 18:37:25,186 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Standard column order: ['Date', 'Details', 'Amount', 'Balance', 'Account', 'Unique Id', 'Credit', 'Debit', 'Source Filename', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'Empty']
2025-06-14 18:37:25,188 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Current columns: ['Account', 'Date', 'Details', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'Credit', 'Debit', 'Amount', 'Balance', 'Source Filename']
2025-06-14 18:37:25,189 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Reordered columns: ['Date', 'Details', 'Amount', 'Balance', 'Account', 'Credit', 'Debit', 'Source Filename', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number']
2025-06-14 18:37:25,198 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying to find handler for file: 38-9004-0646977-01_13Jun.CSV
2025-06-14 18:37:25,203 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] DataFrame shape: (32, 16)
2025-06-14 18:37:25,207 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Column names: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:25,209 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-06-14 18:37:25,211 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankBasicCSVHandler] Checking if file matches: 38-9004-0646977-01_13Jun.CSV
2025-06-14 18:37:25,212 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankBasicCSVHandler] Expected columns: 5, Actual: 16
2025-06-14 18:37:25,213 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankBasicCSVHandler] Column count mismatch: expected 5, got 16
2025-06-14 18:37:25,214 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-06-14 18:37:25,215 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-06-14 18:37:25,217 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking if file matches: 38-9004-0646977-01_13Jun.CSV
2025-06-14 18:37:25,218 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Expected columns: 16, Actual: 16
2025-06-14 18:37:25,220 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking column headers - Expected: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:25,222 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking column headers - Actual: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:25,223 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking account number pattern: 38-?\d{4}-?\d{7}-?\d{2,3}.*
2025-06-14 18:37:25,225 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking account number pattern '38-?\d{4}-?\d{7}-?\d{2,3}.*' in file: 38-9004-0646977-01_13Jun.CSV
2025-06-14 18:37:25,226 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking data at [0,0]: '38-9004-0646977-01'
2025-06-14 18:37:25,229 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Found account number in data
2025-06-14 18:37:25,232 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] File matches handler criteria: 38-9004-0646977-01_13Jun.CSV
2025-06-14 18:37:25,234 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-06-14 18:37:25,237 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Mapping columns: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance'] -> ['Account', 'Date', 'Details', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'Credit', 'Debit', 'Amount', 'Balance']
2025-06-14 18:37:25,239 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] DataFrame shape: (32, 16)
2025-06-14 18:37:25,241 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Sample dates before standardization: ['20-09-2023', '27-08-2024', '07-12-2024']
2025-06-14 18:37:25,244 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Standardizing dates using format: %d/%m/%y
2025-06-14 18:37:25,254 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Date conversion success rate: 100.0% (32/32)
2025-06-14 18:37:25,256 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Standard column order: ['Date', 'Details', 'Amount', 'Balance', 'Account', 'Unique Id', 'Credit', 'Debit', 'Source Filename', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'Empty']
2025-06-14 18:37:25,259 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Current columns: ['Account', 'Date', 'Details', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'Credit', 'Debit', 'Amount', 'Balance', 'Source Filename']
2025-06-14 18:37:25,260 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Reordered columns: ['Date', 'Details', 'Amount', 'Balance', 'Account', 'Credit', 'Debit', 'Source Filename', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number']
2025-06-14 18:37:25,262 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying to find handler for file: 38-9004-0646977-04_13Jun.CSV
2025-06-14 18:37:25,263 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] DataFrame shape: (1343, 16)
2025-06-14 18:37:25,265 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Column names: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:25,265 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying handler: KiwibankBasicCSVHandler
2025-06-14 18:37:25,268 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankBasicCSVHandler] Checking if file matches: 38-9004-0646977-04_13Jun.CSV
2025-06-14 18:37:25,269 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankBasicCSVHandler] Expected columns: 5, Actual: 16
2025-06-14 18:37:25,270 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankBasicCSVHandler] Column count mismatch: expected 5, got 16
2025-06-14 18:37:25,273 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Handler KiwibankBasicCSVHandler did not match
2025-06-14 18:37:25,274 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Trying handler: KiwibankFullCSVHandler
2025-06-14 18:37:25,277 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking if file matches: 38-9004-0646977-04_13Jun.CSV
2025-06-14 18:37:25,279 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Expected columns: 16, Actual: 16
2025-06-14 18:37:25,280 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking column headers - Expected: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:25,282 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking column headers - Actual: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance']
2025-06-14 18:37:25,283 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking account number pattern: 38-?\d{4}-?\d{7}-?\d{2,3}.*
2025-06-14 18:37:25,284 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking account number pattern '38-?\d{4}-?\d{7}-?\d{2,3}.*' in file: 38-9004-0646977-04_13Jun.CSV
2025-06-14 18:37:25,286 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Checking data at [0,0]: '38-9004-0646977-04'
2025-06-14 18:37:25,288 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Found account number in data
2025-06-14 18:37:25,291 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] File matches handler criteria: 38-9004-0646977-04_13Jun.CSV
2025-06-14 18:37:25,293 - DEBUG - fm.modules.update_data.utils.statement_handlers._handler_registry - [handler_registry] Found matching handler: KiwibankFullCSVHandler
2025-06-14 18:37:25,294 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Mapping columns: ['Account number', 'Date', 'Memo/Description', 'Source Code (payment type)', 'TP ref', 'TP part', 'TP code', 'OP ref', 'OP part', 'OP code', 'OP name', 'OP Bank Account Number', 'Amount (credit)', 'Amount (debit)', 'Amount', 'Balance'] -> ['Account', 'Date', 'Details', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'Credit', 'Debit', 'Amount', 'Balance']
2025-06-14 18:37:25,295 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] DataFrame shape: (1343, 16)
2025-06-14 18:37:25,297 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Sample dates before standardization: ['14-06-2023', '15-06-2023', '16-06-2023']
2025-06-14 18:37:25,299 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Standardizing dates using format: %d/%m/%y
2025-06-14 18:37:25,428 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Date conversion success rate: 100.0% (1343/1343)
2025-06-14 18:37:25,431 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Standard column order: ['Date', 'Details', 'Amount', 'Balance', 'Account', 'Unique Id', 'Credit', 'Debit', 'Source Filename', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'Empty']
2025-06-14 18:37:25,432 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Current columns: ['Account', 'Date', 'Details', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'Credit', 'Debit', 'Amount', 'Balance', 'Source Filename']
2025-06-14 18:37:25,433 - DEBUG - fm.modules.update_data.utils.statement_handlers._base_statement_handler - [KiwibankFullCSVHandler] Reordered columns: ['Date', 'Details', 'Amount', 'Balance', 'Account', 'Credit', 'Debit', 'Source Filename', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number']
2025-06-14 18:37:25,622 - INFO - fm.modules.update_data.utils.dw_pipeline - Master file saved to: C:/Users/<USER>/OneDrive/Documents/ACCOUNTS/Bank_Statement_dwnlds_2025\fmMaster.csv
2025-06-14 18:37:25,623 - INFO - fm.modules.update_data.utils.dw_pipeline - Sample dates for database: ['2023-06-19', '2023-06-20', '2023-06-20']
2025-06-14 18:37:25,625 - INFO - fm.modules.update_data.utils.dw_pipeline - Sample dates for database: ['2023-06-19', '2023-06-20', '2023-06-20']
2025-06-14 18:37:25,626 - INFO - fm.modules.update_data.utils.dw_pipeline - Sending 2099 records to database
2025-06-14 18:37:25,628 - INFO - fm.database_service.repository.sqlite_repository - Adding 2099 transactions to database from DataFrame
2025-06-14 18:37:25,630 - INFO - fm.database_service.repository.sqlite_repository - DataFrame columns: ['Date', 'Details', 'Amount', 'Balance', 'Account', 'Credit', 'Debit', 'Source Filename', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number']
2025-06-14 18:37:25,633 - INFO - fm.database_service.repository.sqlite_repository - Sample dates before processing: ['2023-06-19', '2023-06-20', '2023-06-20']
2025-06-14 18:37:25,642 - INFO - fm.database_service.repository.sqlite_repository - Mapped column 'Date' to database column 'date'
2025-06-14 18:37:25,644 - INFO - fm.database_service.repository.sqlite_repository - Mapped column 'Details' to database column 'description'
2025-06-14 18:37:25,646 - INFO - fm.database_service.repository.sqlite_repository - Mapped column 'Amount' to database column 'amount'
2025-06-14 18:37:25,648 - INFO - fm.database_service.repository.sqlite_repository - Mapped column 'Balance' to database column 'balance'
2025-06-14 18:37:25,649 - INFO - fm.database_service.repository.sqlite_repository - Mapped column 'Account' to database column 'account_number'
2025-06-14 18:37:25,653 - INFO - fm.database_service.repository.sqlite_repository - Mapped column 'Source Filename' to database column 'source_file'
2025-06-14 18:37:25,656 - INFO - fm.database_service.repository.sqlite_repository - Mapped column 'import_date' to database column 'import_date'
2025-06-14 18:37:25,658 - INFO - fm.database_service.repository.sqlite_repository - Mapped column 'modified_date' to database column 'modified_date'
2025-06-14 18:37:25,659 - INFO - fm.database_service.repository.sqlite_repository - Mapped column 'is_deleted' to database column 'is_deleted'
2025-06-14 18:37:25,662 - INFO - fm.database_service.repository.sqlite_repository - Creating temporary table for import
2025-06-14 18:37:25,785 - INFO - fm.database_service.repository.sqlite_repository - Temp table columns: ['date', 'description', 'amount', 'balance', 'account_number', 'Credit', 'Debit', 'source_file', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'import_date', 'modified_date', 'is_deleted']
2025-06-14 18:37:25,786 - INFO - fm.database_service.repository.sqlite_repository - Transaction table columns: ['Date', 'Details', 'Amount', 'Balance', 'Account', 'Unique Id', 'Credit', 'Debit', 'Source Filename', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'Empty', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-06-14 18:37:25,788 - INFO - fm.database_service.repository.sqlite_repository - Using columns for import: ['Credit', 'Debit', 'Payment Type', 'TP Ref', 'TP Part', 'TP Code', 'OP Ref', 'OP Part', 'OP Code', 'OP Name', 'OP Bank Account Number', 'import_date', 'modified_date', 'is_deleted']
2025-06-14 18:37:25,791 - INFO - fm.database_service.repository.sqlite_repository - Sample data from temp table: [('2023-06-19', 'WINZ Q SHAW-WILLIC61361890457 W&I Benefit ;Ref: Q SHAW-WILLIC61361890457 W&I Benefit', 338.2, 666.0, '38-9004-0646977-00', 338.2, None, '38-9004-0646977-00_13Jun.CSV', 'SW', None, 'Q SHAW-WILLI', 'C61361890457', None, None, None, 'W&I Benefit', '03-0049-0005363-26', '2025-06-14T18:37:25.631437', '2025-06-14T18:37:25.631437', 0), ('2023-06-20', 'AP#******** TO TWO DEGREES MOBILE LIMITED (MOBI ;2 degrees catch up pym nt', -16.0, 650.0, '38-9004-0646977-00', None, 16.0, '38-9004-0646977-00_13Jun.CSV', None, 'nt', '2 degrees', 'catch up pym', '1983949', '**********', 'Quinn Shaw-W', 'TWO DEGREES MOBILE LIMITED (MOBI', '02-0192-0122220-01', '2025-06-14T18:37:25.631437', '2025-06-14T18:37:25.631437', 0), ('2023-06-20', 'AP#******** TO Q M SHAW-WILLIAMS ;Transfer to Q M SHAW-WILLIAMS - 04', -2.5, 647.5, '38-9004-0646977-00', None, 2.5, '38-9004-0646977-00_13Jun.CSV', None, 'on', 'Microsoft 36', '5 subscripti', 'on', 'Microsoft 36', '5 subscripti', 'Q M SHAW-WILLIAMS', '38-9004-0646977-04', '2025-06-14T18:37:25.631437', '2025-06-14T18:37:25.631437', 0)]
2025-06-14 18:37:25,792 - INFO - fm.database_service.repository.sqlite_repository - Inserting non-duplicate records
2025-06-14 18:37:25,796 - DEBUG - fm.database_service.repository.sqlite_repository - Executing insert query: 
                INSERT INTO transactions ("Credit", "Debit", "Payment Type", "TP Ref", "TP Part", "TP Code", "OP Ref", "OP Part", "OP Code", "OP Name", "OP Bank Account Number", "import_date", "modified_date", "is_deleted")
                SELECT "Credit", "Debit", "Payment Type", "TP Ref", "TP Part", "TP Code", "OP Ref", "OP Part", "OP Code", "OP Name", "OP Bank Account Number", "import_date", "modified_date", "is_deleted" FROM temp_import t
                WHERE NOT EXISTS (
                    SELECT 1 FROM transactions 
                    WHERE 
                    "date" = t."date" 
                    AND "description" = t."description" 
                    AND "amount" = t."amount"
                    AND (("account_number" IS NULL AND account_number IS NULL) OR "account_number" = account_number)
                
                    AND is_deleted = 0
                )
                
2025-06-14 18:37:25,808 - ERROR - fm.database_service.repository.sqlite_repository - Error adding transactions to database: NOT NULL constraint failed: transactions.Date
2025-06-14 18:37:25,820 - ERROR - fm.database_service.repository.sqlite_repository - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\database_service\repository\sqlite_repository.py", line 289, in add_transactions_from_df
    cursor.execute(insert_query)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^
sqlite3.IntegrityError: NOT NULL constraint failed: transactions.Date

2025-06-14 18:37:25,825 - INFO - fm.database_service.repository.sqlite_repository - Temporary table dropped
2025-06-14 18:37:25,849 - INFO - fm.modules.update_data.utils.dw_pipeline - Database updated: 0 added, 0 duplicates
2025-06-14 18:37:25,854 - INFO - fm.modules.update_data.ud_presenter - File processing completed
