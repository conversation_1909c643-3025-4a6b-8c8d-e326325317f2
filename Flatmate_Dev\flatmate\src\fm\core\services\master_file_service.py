"""Master file tracking service for FlatMate application."""

from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

from ..config import config
from ..services.event_bus import global_event_bus
from ..services.logger import log_this, LogLevel, log

class MasterFileService:
    """Service for tracking and managing master files.
    
    Provides centralized management of master file locations and history.
    Integrates with core config for storage and event bus for notifications.
    """
    
    _instance = None
    
    def __new__(cls):
        """Singleton pattern implementation."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize master file service."""
        if not hasattr(self, 'initialized'):
            self.config = config
            self.events = global_event_bus
            self.initialized = True
            log('MasterFileService initialized', 'd')
    
    @log_this(LogLevel.INFO)
    def update_location(self, file_path: Path) -> None:
        """Update master file location and maintain history.
        
        Args:
            file_path: Path to new master file
            
        Raises:
            ValueError: If file_path is invalid or doesn't exist
        """
        if not file_path or not file_path.exists():
            raise ValueError(f"Invalid master file: {file_path}")
            
        # Update current master location
        self.config.set_value('paths.master', str(file_path))
        
        # Update history
        history = self.config.get_value('paths.master_history', default=[])
        history.append({
            'path': str(file_path),
            'timestamp': datetime.now().isoformat()
        })
        if len(history) > 10:  # Keep last 10
            history = history[-10:]
        self.config.set_value('paths.master_history', history)
        
        # Emit event
        self.events.publish('master_file_changed', {
            'path': str(file_path),
            'timestamp': datetime.now().isoformat()
        })
        log(f'Master file location updated to: {file_path}', 'i')
    
    @log_this(LogLevel.DEBUG)
    def get_current_master(self) -> Optional[Path]:
        """Get current master file location.
        
        Returns:
            Path to current master file, or None if not set
        """
        path_str = self.config.get_value('paths.master')
        return Path(path_str) if path_str else None
    
    @log_this(LogLevel.DEBUG)
    def get_history(self) -> List[Dict]:
        """Get master file history.
        
        Returns:
            List of dictionaries containing historical master file locations
            Each dict has 'path' and 'timestamp' keys
        """
        return self.config.get_value('paths.master_history', default=[])
    
    @log_this(LogLevel.DEBUG)
    def validate_master(self, file_path: Path) -> bool:
        """Validate if file can be used as master.
        
        Args:
            file_path: Path to file to validate
            
        Returns:
            True if file is valid master file, False otherwise
        """
        if not file_path.exists():
            log(f'Master file does not exist: {file_path}', 'w')
            return False
            
        if file_path.suffix.lower() != '.csv':
            log(f'Invalid master file type: {file_path.suffix}', 'w')
            return False
            
        # Add more validation as needed
        return True


# Global instance
master_file_service = MasterFileService()
