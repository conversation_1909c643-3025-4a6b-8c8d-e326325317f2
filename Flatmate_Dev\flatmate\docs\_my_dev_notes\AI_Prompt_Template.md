AI_Prompt_Template.md 


## Task Overview
[Brief description of what needs to be done]

## Relevant Files
- [@file_path](/absolute/path/to/file)
- [@another_file](/absolute/path/to/another_file)

## Context
- Current system state and behavior
- Existing architecture and patterns in use
- Relevant components and their relationships
- Integration points with other systems

## Requirements
1. Primary objectives
2. Functional requirements
3. Performance requirements
4. Integration requirements

## Architectural Principles
- Follow clean architectural boundaries
- Maintain proper interfaces between components
- Use pub/sub pattern for events
- Prefer self-documenting code over extensive comments
- Use UK English spelling

## Specific Constraints
- List any technical limitations
- Integration requirements
- Performance requirements
- Compatibility needs

## Proposed Approach
- High-level solution overview
- Key architectural decisions
- Integration strategy
- Migration steps (if applicable)

## Questions for Discussion
- List any architectural decisions that need discussion
- Alternative approaches to consider
- Potential impacts on other systems

---
Note: This template emphasizes clean architecture, proper interfaces, and maintainable code as per established project preferences.