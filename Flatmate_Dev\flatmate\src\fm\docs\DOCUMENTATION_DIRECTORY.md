# Flatmate App Documentation Directory

## Overview

This document serves as a comprehensive directory of all documentation for the Flatmate App. It provides descriptions of what each document covers and links to the relevant files.

## Central Documentation

- [System Overview](/flatmate/src/fm/docs/SYSTEM_OVERVIEW.md) - Comprehensive overview of the entire system architecture, components, and design principles

## Architecture Documentation

| Document | Description | Location |
|----------|-------------|----------|
| [Core Architecture](/flatmate/src/fm/docs/architecture/core_architecture.md) | Fundamental architectural principles and patterns | Module-specific |
| [Feature Driven Architecture](/flatmate/src/fm/docs/architecture/feature_driven_architecture.md) | Organization of features and their implementation | Module-specific |
| [Modular Feature Architecture](/flatmate/src/fm/docs/architecture/modular_feature_architecture.md) | How features are structured as modules | Module-specific |
| [Module Initialization Pattern](/flatmate/src/fm/docs/architecture/module_initialization_pattern.md) | Patterns for initializing modules | Module-specific |
| [Module Wrapper Pattern](/flatmate/src/fm/docs/architecture/module_wrapper_pattern.md) | Patterns for module encapsulation | Module-specific |
| [Services System](/flatmate/src/fm/docs/architecture/services_system.md) | Service-oriented architecture implementation | Module-specific |
| [Theme System](/flatmate/src/fm/docs/architecture/theme_system.md) | UI theming architecture | Module-specific |
| [Module System](/DOCS/architecture/module_system.md) | Overall module system design | Workspace-level |
| [Event Bus Design](/DOCS/architecture/event_bus_design.md) | Pub/sub event system architecture | Workspace-level |
| [Config System](/DOCS/architecture/config_system.md) | Configuration management architecture | Workspace-level |
| [Module Transitions](/DOCS/architecture/module_transitions.md) | Navigation between modules | Workspace-level |
| [Qt Implementation](/DOCS/architecture/qt_implementation.md) | Qt framework integration | Workspace-level |
| [Styling System](/DOCS/architecture/styling_system.md) | UI styling architecture | Workspace-level |
| [Update Data Module](/DOCS/architecture/update_data_module.md) | Update Data module architecture | Workspace-level |

## UI System Documentation

| Document | Description | Location |
|----------|-------------|----------|
| [InfoBar Architecture](/flatmate/src/fm/docs/ui_system/InfoBar_Architecture_v1.1.md) | Architecture of the InfoBar component | Module-specific |
| [InfoBar Usage](/flatmate/src/fm/docs/ui_system/InfoBar_Usage.md) | How to use the InfoBar component | Module-specific |
| [QStatusBar Implementation](/flatmate/src/fm/docs/_ai_update/2025-03-19_qstatusbar_implementation.md) | Implementation of QStatusBar replacing InfoBar | Module-specific |
| [UI System Design](/DOCS/UI_system_design/UI_system_design.md) | Overall UI system architecture | Workspace-level |
| [GUI System Design](/DOCS/UI_system_design/gui_system_design.md) | GUI component architecture | Workspace-level |
| [Widget Styling System](/DOCS/UI_system_design/widget_styling_system.md) | Widget styling architecture | Workspace-level |
| [Widget Styling Quick Reference](/DOCS/UI_system_design/widget_styling_quick_reference.md) | Quick reference for widget styling | Workspace-level |

## Module Documentation

| Document | Description | Location |
|----------|-------------|----------|
| [Update Data View README](/flatmate/src/fm/modules/update_data/_view/README.md) | Overview of the Update Data view component | Module-specific |
| [Update Data Config README](/flatmate/src/fm/modules/update_data/config/README.md) | Configuration for the Update Data module | Module-specific |
| [Update Data Services README](/flatmate/src/fm/modules/update_data/services/README.md) | Services used by the Update Data module | Module-specific |
| [Module Structure](/DOCS/module_structure.md) | General module structure guidelines | Workspace-level |
| [Module Status](/DOCS/architecture/module_status.md) | Status tracking for modules | Workspace-level |
| [Home Module](/DOCS/App_Status/home_module.md) | Home module documentation | Workspace-level |
| [Home Module Implementation](/DOCS/App_Status/home_module_implementation.md) | Implementation details for Home module | Workspace-level |
| [Onboarding Module](/DOCS/App_Status/onboarding_module.md) | Onboarding module documentation | Workspace-level |
| [Profiles Implementation](/DOCS/App_Status/profiles_implementation.md) | Implementation details for Profiles | Workspace-level |

## Development Guides

| Document | Description | Location |
|----------|-------------|----------|
| [Development Environment](/DOCS/dev_env.md) | Setting up the development environment | Workspace-level |
| [House Style](/DOCS/HOUSE_STYLE.md) | Coding standards and style guidelines | Workspace-level |
| [Refactoring Guide](/DOCS/Refactoring/dev_tools_refactoring.md) | Guidelines for refactoring code | Workspace-level |
| [Pre-commit Notes](/DOCS/Refactoring/pre_commit_notes.md) | Pre-commit hook configuration | Workspace-level |
| [Rope Usage Guide](/DOCS/Refactoring/rope_usage_guide.md) | Using Rope for refactoring | Workspace-level |

## Project Documentation

| Document | Description | Location |
|----------|-------------|----------|
| [Architectural Approach](/ARCHITECTURAL_APPROACH.md) | High-level architectural principles | Workspace-level |
| [Business Model](/BUSINESS_MODEL.md) | Business model and objectives | Workspace-level |
| [Flatmate Vision](/FLATMATE_VISION.md) | Vision and goals for the Flatmate App | Workspace-level |

## Resources

| Document | Description | Location |
|----------|-------------|----------|
| [Icon Reference (App)](/flatmate/resources/icons/icon_reference.md) | Reference for application icons | Module-specific |
| [Icon Reference](/DOCS/Reference/Icon_Links.md) | General icon reference | Workspace-level |
| [QT Font Size](/DOCS/QT_FontSize.md) | Font size guidelines for Qt | Workspace-level |

## Roadmap and Planning

| Document | Description | Location |
|----------|-------------|----------|
| [Roadmap (March 2025)](/DOCS/RoadMap/roadmap_250316.md) | Current roadmap | Workspace-level |
| [Full App Roadmap](/DOCS/RoadMap/roadmap_for_full_app.md) | Long-term roadmap | Workspace-level |
| [To Implement](/DOCS/RoadMap/to_implement.md) | Features to be implemented | Workspace-level |
| [Tasks](/DOCS/RoadMap/z_tasks.md) | Current tasks | Workspace-level |
| [Pending Tasks](/flatmate/_pending_tasks.md) | Pending development tasks | Module-specific |

## Changelog

| Document | Description | Location |
|----------|-------------|----------|
| [Changelog](/DOCS/CHANGELOG.md) | Record of changes to the application | Workspace-level |

## Documentation Maintenance

When adding new documentation:

1. Keep READMEs in the folders where they are relevant
2. Update this directory document with a link to the new file
3. Ensure the new documentation follows the [Documentation Style Guide](/DOCS/DOCUMENTATION_STYLE_GUIDE.md)
4. Archive outdated documentation in `/flatmate/src/fm/docs/archive`

## Documentation Review Process

To maintain accurate and relevant documentation:

1. Review documentation quarterly for accuracy
2. Archive or update outdated documentation
3. Ensure all new features have corresponding documentation
4. Keep the System Overview document up to date with architectural changes
