from typing import Dict, Any, List
from datetime import datetime
from ..profiles.base_profile import BaseProfile

class Account(BaseProfile):
    """Class for managing financial accounts in the flatmate app."""
    
    def __init__(self, profile_id: str = None):
        super().__init__(profile_id)
        self.account_name: str = ""
        self.account_type: str = ""  # savings, checking, etc.
        self.currency: str = "USD"
        self.balance: float = 0.0
        self.owner_id: str = ""  # ID of the profile that owns this account
        self.shared_with: List[str] = []  # IDs of profiles that can view/manage this account
        self.created_date: str = datetime.now().isoformat()
        self.last_updated: str = self.created_date
        self.description: str = ""
        self.tags: List[str] = []
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the account to a dictionary."""
        data = super().to_dict()
        data.update({
            'account_name': self.account_name,
            'account_type': self.account_type,
            'currency': self.currency,
            'balance': self.balance,
            'owner_id': self.owner_id,
            'shared_with': self.shared_with,
            'created_date': self.created_date,
            'last_updated': self.last_updated,
            'description': self.description,
            'tags': self.tags
        })
        return data
    
    def from_dict(self, data: Dict[str, Any]) -> None:
        """Load account data from a dictionary."""
        super().from_dict(data)
        self.account_name = data.get('account_name', '')
        self.account_type = data.get('account_type', '')
        self.currency = data.get('currency', 'USD')
        self.balance = data.get('balance', 0.0)
        self.owner_id = data.get('owner_id', '')
        self.shared_with = data.get('shared_with', [])
        self.created_date = data.get('created_date', datetime.now().isoformat())
        self.last_updated = data.get('last_updated', self.created_date)
        self.description = data.get('description', '')
        self.tags = data.get('tags', [])
