"""ASB Bank standard CSV format mapping."""

from dataclasses import dataclass

import pandas as pd

from ._base_statement_handler import StatementHandler
from fm.core.standards.fm_standard_columns import StandardColumns


@dataclass
class AsbStandardCSVHandler(StatementHandler):
    """ASB Bank standard CSV format mapping."""

    def __post_init__(self):
        # Statement format definition
        self.statement_format = self.StatementFormat(
            bank_name="ASB",
            variant="standard",  # Standard CSV format
            file_type="csv",
        )

        # Account number pattern and location
        # ASB includes account in metadata: "Bank 12; Branch 3053; Account 0478706-50"
        self.account_num_attrs = self.AccountNumberAttributes(
            pattern=r"Bank 12; Branch \d+; Account [\d-]+",  # Matches ASB account format
            location=(1, 0),  # Second line (1), first column (0)
            in_metadata=True,  # Account is in metadata
        )

        # Metadata structure
        self.source_metadata_attrs = self.SourceMetadataAttributes(
            has_metadata_rows=True,
            metadata_start=(0, 0),  # Starts at first line
            metadata_end=(6, 0),  # Ends at line 6 (before headers)
        )

        # Column mapping
        self.column_attrs = self.ColumnAttributes(
            has_col_names=True,  # File has column names
            n_source_cols=7,
            col_names_in_header=False,  # Column names are in the header section
            has_account_column=False,  # Account number is in metadata
            col_names_row=7,  # Column names are on row 7
            source_col_names=[
                "Date",
                "Unique Id",
                "Transaction Type",
                "Cheque Number",  # Always empty in ASB exports
                "Payee",
                "Memo",
                "Amount"
            ],
            target_col_names=[
                StandardColumns.DATE,
                StandardColumns.UNIQUE_ID,  # Bank's unique transaction identifier
                StandardColumns.PAYMENT_TYPE,  # Transaction type (EFTPOS, DEBIT, etc)
                None,  # Skip Cheque Number column (always empty in ASB)
                StandardColumns.OP_NAME,  # Keep payee separately
                StandardColumns.DETAILS,  # Keep memo separately and also use for combined details
                StandardColumns.AMOUNT,
            ],
            date_format='%Y/%m/%d',  # ASB uses YYYY/MM/DD format
        )

    def _custom_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle ASB-specific formatting:
        - Combine Payee and Memo into Details for better transaction descriptions"""
        
        # Create combined details from payee and memo
        payee = df[StandardColumns.OP_NAME.value].fillna('')
        memo = df[StandardColumns.DETAILS.value].fillna('')
        df[StandardColumns.DETAILS.value] = payee + ' ' + memo
        df[StandardColumns.DETAILS.value] = df[StandardColumns.DETAILS.value].str.strip()
        
        # Fill any empty columns with empty string
        df = df.fillna('')
        
        return df
