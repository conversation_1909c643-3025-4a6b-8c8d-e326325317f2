"""
Test script for the enhanced column system.

This script tests the new column infrastructure to ensure it works correctly
before we start migrating existing code.
"""

import sys
from pathlib import Path

# Add the src directory to the path so we can import our modules
src_path = Path(__file__).parent.parent.parent
sys.path.insert(0, str(src_path))

try:
    from fm.core.standards import (
        StandardColumns, 
        get_column_manager, 
        get_column_preferences,
        ColumnManager,
        ColumnPreferences
    )
    print("✅ Successfully imported enhanced column system")
except ImportError as e:
    print(f"❌ Failed to import enhanced column system: {e}")
    sys.exit(1)


def test_basic_column_definitions():
    """Test basic column definition functionality."""
    print("\n🧪 Testing basic column definitions...")
    
    # Test accessing column properties
    date_col = StandardColumns.DATE
    print(f"Date column - db_name: {date_col.db_name}, display_name: {date_col.display_name}")
    
    details_col = StandardColumns.DETAILS
    print(f"Details column - db_name: {details_col.db_name}, field_name: {details_col.field_name}, display_name: {details_col.display_name}")
    
    # Test finding columns
    found_col = StandardColumns.find_by_db_name("details")
    if found_col:
        print(f"✅ Found column by db_name: {found_col.display_name}")
    else:
        print("❌ Failed to find column by db_name")
    
    # Test mappings
    field_to_db = StandardColumns.get_field_to_db_mapping()
    print(f"Field to DB mapping sample: description -> {field_to_db.get('description', 'NOT FOUND')}")
    
    db_to_display = StandardColumns.get_db_to_display_mapping()
    print(f"DB to Display mapping sample: details -> {db_to_display.get('details', 'NOT FOUND')}")


def test_column_manager():
    """Test column manager functionality."""
    print("\n🧪 Testing column manager...")
    
    manager = get_column_manager()
    
    # Test getting display columns for categorize module
    display_columns = manager.get_display_columns_for_module("categorize")
    print(f"Display columns for categorize: {display_columns}")
    
    # Test column display mapping
    display_mapping = manager.get_column_display_mapping("categorize")
    print(f"Display mapping for categorize: {display_mapping}")
    
    # Test column widths
    widths = manager.get_column_widths("categorize")
    print(f"Column widths for categorize: {widths}")
    
    # Test editable columns
    editable = manager.get_editable_columns("categorize")
    print(f"Editable columns for categorize: {editable}")


def test_transaction_conversion():
    """Test converting transaction-like data to DataFrame."""
    print("\n🧪 Testing transaction conversion...")
    
    # Mock transaction data (like Transaction.__dict__)
    mock_transactions = [
        {
            'date': '2025-01-15',
            'description': 'Grocery Store Purchase',
            'amount': -45.67,
            'account_number': '12345',
            'tags': 'groceries,food',
            'category': 'Food & Dining',
            'transaction_id': 1
        },
        {
            'date': '2025-01-16', 
            'description': 'Salary Deposit',
            'amount': 2500.00,
            'account_number': '12345',
            'tags': 'income,salary',
            'category': 'Income',
            'transaction_id': 2
        }
    ]
    
    manager = get_column_manager()
    
    # Test field to db conversion
    db_dict = manager.convert_transaction_dict_to_db_dict(mock_transactions[0])
    print(f"Converted transaction dict: {db_dict}")
    
    # Test DataFrame creation
    import pandas as pd
    
    # Simulate the old problematic way
    old_df = pd.DataFrame(mock_transactions)
    print(f"Old DataFrame columns: {old_df.columns.tolist()}")
    
    # Test the new way
    converted_data = [manager.convert_transaction_dict_to_db_dict(t) for t in mock_transactions]
    new_df = pd.DataFrame(converted_data)
    print(f"New DataFrame columns: {new_df.columns.tolist()}")
    
    # Test prepare for display
    filtered_df, column_mapping, column_widths = manager.prepare_dataframe_for_display(new_df, "categorize")
    print(f"Prepared for display - columns: {filtered_df.columns.tolist()}")
    print(f"Column mapping: {column_mapping}")


def test_column_preferences():
    """Test column preferences system."""
    print("\n🧪 Testing column preferences...")
    
    preferences = get_column_preferences()
    
    # Test getting module preferences
    cat_prefs = preferences.get_module_preferences("categorize")
    print(f"Categorize visible columns: {cat_prefs.visible_columns}")
    print(f"Categorize column order: {cat_prefs.column_order}")
    
    # Test setting custom display name
    preferences.set_custom_display_name("categorize", "details", "Transaction Details")
    custom_name = preferences.get_custom_display_name("categorize", "details")
    print(f"Custom display name for details: {custom_name}")
    
    # Reset it
    preferences.set_custom_display_name("categorize", "details", "")


def test_migration_compatibility():
    """Test backward compatibility and migration features."""
    print("\n🧪 Testing migration compatibility...")
    
    from fm.core.standards.migration_utils import get_migration_helper
    
    helper = get_migration_helper()
    
    # Test legacy column conversion
    legacy_columns = ["date", "details", "amount", "account", "tags", "category"]
    converted = helper.convert_legacy_display_columns(legacy_columns)
    print(f"Legacy columns converted: {legacy_columns} -> {converted}")
    
    # Test migration suggestions
    suggestions = helper.get_migration_suggestions("categorize")
    print(f"Migration suggestions for categorize module:")
    print(f"  Recommended columns: {len(suggestions['recommended_columns'])}")
    print(f"  Breaking changes: {len(suggestions['breaking_changes'])}")


def main():
    """Run all tests."""
    print("🚀 Testing Enhanced Column System Infrastructure")
    print("=" * 60)
    
    try:
        test_basic_column_definitions()
        test_column_manager()
        test_transaction_conversion()
        test_column_preferences()
        test_migration_compatibility()
        
        print("\n" + "=" * 60)
        print("✅ All tests completed successfully!")
        print("🎉 Enhanced column system infrastructure is ready!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
