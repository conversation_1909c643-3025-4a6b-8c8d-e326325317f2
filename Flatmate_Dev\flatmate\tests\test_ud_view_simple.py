"""Test that our base class works."""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from PySide6.QtWidgets import QApplication, QPushButton
from fm.modules.base.base_module_view import BaseModuleView

class TestView(BaseModuleView):
    def setup_ui(self):
        self.button = QPushButton("Test")
    
    def setup_left_panel(self, layout):
        layout.addWidget(self.button)

app = QApplication([])
view = TestView()
assert view is not None
assert hasattr(view, 'button')
print("Test passed!")
