"""
UpdateData InfoBar widget for displaying status messages in the Update Data module.
"""

from fm.core.services.event_bus import Events, global_event_bus
from fm.gui.widgets.info_bar import InfoBar
from PySide6.QtCore import Qt
from PySide6.QtWidgets import <PERSON>H<PERSON><PERSON>Layout, QProgressBar


class UpdateDataInfoBar(InfoBar):
    """InfoBar widget specialized for the Update Data module."""

    def __init__(self, parent=None):
        """Initialize the Update Data info bar widget."""
        super().__init__(parent)
        self._customize_appearance()
        self._add_progress_bar()

        # Subscribe to events
        self.event_bus = global_event_bus
        self.event_bus.subscribe(Events.INFO_MESSAGE, self._handle_info_message)
        self.event_bus.subscribe(Events.INFO_CLEAR, self._handle_info_clear)

    def _customize_appearance(self):
        """Customize the appearance to make it more discrete."""
        # Update layout margins to remove left/right padding and minimize vertical padding
        layout = self.layout()
        layout.setContentsMargins(0, 2, 0, 2)

        # Make the background slightly lighter than the app background
        # Assuming dark theme with background close to #222
        self.setStyleSheet(
            """
            background-color: #2a2a2a; 
            border-top: 1px solid #333;
        """
        )

        # Update text color for better visibility on dark background
        self.message_label.setStyleSheet("color: #aaa; font-style: italic;")

        # Reduce the height
        self.setFixedHeight(24)

    def _add_progress_bar(self):
        """Add a progress bar to the info bar."""
        # Create progress bar (hidden by default)
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setFixedWidth(150)
        self.progress_bar.setVisible(False)

        # Add progress bar to layout
        layout = self.layout()
        layout.setSpacing(5)
        # Add to layout with right alignment
        layout.addWidget(self.progress_bar)
        # Set alignment for the widget
        layout.setAlignment(self.progress_bar, Qt.AlignmentFlag.AlignRight)

    def set_status(self, message, is_error=False):
        """Set the status message to display.

        Args:
            message: The message text to display
            is_error: Whether this is an error message
        """
        if not message:
            self.clear()
            return

        # Apply error styling if needed
        if is_error:
            self.message_label.setStyleSheet("color: #d32f2f; font-weight: bold;")
        else:
            self.message_label.setStyleSheet("color: #444; font-style: italic;")

        # Set the message text directly
        self.message_label.setText(message)
        # Make the widget visible
        self.setVisible(True)

    def set_progress(self, current, total):
        """Show progress information.

        Args:
            current: Current progress value
            total: Total progress value
        """
        if total <= 0:
            self.progress_bar.setVisible(False)
            return

        # Calculate percentage
        percentage = min(100, int((current / total) * 100))

        # Update progress bar
        self.progress_bar.setValue(percentage)
        self.progress_bar.setVisible(True)

        # Show the widget
        self.setVisible(True)

    def set_error(self, message):
        """Display an error message.

        Args:
            message: The error message to display
        """
        self.set_status(message, is_error=True)

    def clear(self):
        """Clear all displays."""
        # Clear the message using the parent method
        super().clear()

        # Also hide the progress bar
        self.progress_bar.setVisible(False)

    # Event handlers
    def _handle_info_message(self, message):
        """Handle info messages from the event bus."""
        if message:
            self.set_status(message)

    def _handle_info_clear(self, _=None):
        """Handle clear events from the event bus."""
        self.clear()
