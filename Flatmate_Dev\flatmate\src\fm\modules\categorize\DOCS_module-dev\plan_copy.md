



No file chosen
Theme:














Flatmate MVP: Categorisation Module Integration
Notes
User wants to focus on a minimum viable product (MVP) for bank statement parsing, database interface, and transaction categorisation.
The current design doc is outdated; new categorisation functionality needs to be integrated.
The app uses a vessel GUI, with modules inheriting from a base view.
The update_data and home modules are the most complete; categorize module is a stub.
The design doc proposes a modular processor architecture with handler and categorizer classes, but this is not fully reflected in the current codebase.
The categorize module currently contains only init.py and pattern_learner.py; needs expansion to match other modules.
User has a cleaner version on another machine to merge in later.
User requested the integration/architecture plan be put into a suitably named markdown document before further code changes.
Integration/architecture plan markdown document created; now incorporates core processing logic (​dw_director.py​, ​dw_pipeline.py​, ​statement_handlers​).
User agreed to phased approach: reuse update_data pipeline for now, defer moving core processing logic to shared module until after MVP is stable.
User suggested creating a core shims/api package now, to provide a stable import point for statement processing logic (initially forwarding to update_data.utils, to ease future refactor).
Core shims/api package (​fm.statement_processing​) created and initial categorize core scaffold started.
Need to create a cat_transactions module architecture/GUI layout document to discuss module structure and UI.
cat_transactions module architecture/GUI layout document created.
High-level architecture overview and code review complete.
Main entry points: main.py, module_coordinator.py, home, gui, update_data, categorize.
process_files shim now implemented as a thin wrapper using pipeline helpers, not direct import from dw_director; ImportError resolved.
Need to create a design/API document (lightweight PRD) to clarify the responsibilities and intended API of the statement processing layer (shim vs. director).
User clarified: for MVP, categorize module should focus on categorising/editing transactions already in the database, not duplicating file import logic (handled by Update Data). File import UI/logic should not be replicated in categorize; PRD should reflect this split.
Ad-hoc file opening in Categorise is supported for non-DB-affecting, one-off categorisation (in addition to DB-driven workflow).
UI/UX notes: consider filtering out unwanted time (00:00:00) from date columns, making columns resizable, and aligning left-panel button styles with Update-Data's option buttons.
Add to design doc: evaluate if current spreadsheet/table widget is best for categorisation ergonomics; discuss possible alternatives and improvements for viewing/editing categories efficiently.
UI polish sprint: date formatting (strip 00:00:00), column resizing, align button styles with OptionButton from Update-Data.  [date formatting, column resizing, left-panel button styling/layout complete]
Ergonomics/design doc sprint: evaluate table widget alternatives, category editing UX, keyboard shortcuts, split-view, filter/search row.
Note: Need to cache opened files for editing so navigation doesn’t cause reloads or lost changes.
DEV_NOTES: Prefer a core caching service for file/data edits (not module coordinator); consider temp file persistence for open views and cache expiry for large/unused data.
qtpandas is not viable (unmaintained ~10 years).
Dates should be sorted; columns must be resizable, sortable, filterable; add option to hide/show columns (default: required columns only).
Categorisation workflow needs utility column with button/dropdown per row, and persistent/savable filter terms—warrants its own design doc.
Consider top-based menu for more horizontal space (future revision); table view widget needs further improvement for modern look and ergonomics.
Task List
 Review the current design doc and note discrepancies.
 Review the structure and flow of main.py, module_coordinator, and module views.
 Assess the current state of the categorize module and requirements for a standard module.
 Provide a high-level overview of the current architecture and module system.
 Conduct a code review for maintainability and extensibility.
 Recommend steps to flesh out and integrate the categorize module.
 ## Actionable Task List (MVP Sprint)

### 1. Core Back-End
- [ ] Extend `DataService.get_transactions(start_date, end_date, account_id)`
- [ ] Add `tags` column to transactions table (comma-separated multi-word tags)
- [ ] Create DB migration script and update ORM/dataclass

### 2. Caching Service
- [ ] Implement `fm.core.cache.CacheService` (in-memory + temp-file spill)
- [ ] Presenter integration: save edited DataFrame on `cleanup()`, restore on `initialize()`

### 3. Categorise UI Enhancements
- [ ] Add date-range picker + account dropdown above table
- [ ] Replace category column with editable `tags` column (comma-separated)
- [ ] Make canonical columns read-only in model
- [ ] Add "⚙" utility column with per-row dropdown actions (quick-set tag, add pattern, mark reviewed)
- [ ] Column visibility toggle (show required by default)
- [ ] Implement filter row via `QSortFilterProxyModel`
- [ ] Default sort: date descending

### 4. Save & Validation Workflow
- [ ] Implement “Save Tags” action writing only `tags` column back to DB
- [ ] Validate no edits to canonical columns before save
- [ ] Prompt on unsaved changes when navigating away

### 5. Pattern Editing UI
- [ ] Create simple dialog to edit regex→tag rules
- [ ] Persist rules JSON and reload on start

### 6. Documentation & Ergonomics
- [ ] Finalise ergonomics_notes.md with decisions/outstanding questions
- [ ] Keep this task list in sync; tick items on completion
 Create core shims/api package for statement processing logic and update categorize scaffold/design doc accordingly.
 Create and maintain an integration/architecture plan markdown document as discussed.
 Create categorize presenter/view and register in coordinator.
 Create cat_transactions module architecture/GUI layout document.
 Integrate categorize module with sidebar/nav button in Home module.
 Add Save-to-DB action to categorize module.
 Implement pattern editing UI for categorisation patterns.
 Resolve ImportError: process_files missing in dw_director.py/shim before further categorize integration.
 Create design/API document (lightweight PRD) for statement processing layer responsibilities and API.
 Update PRD to clarify categorize module's role: DB-driven categorisation, not file import duplication; ad-hoc file path is supported for non-DB flows.
 Implement Categorise view/presenter logic for both DB-driven and ad-hoc file categorisation flows.
 [Sprint] UI polish: date formatting, column resizing, button style alignment.
 [Sprint] Ergonomics/design doc: table widget alternatives, category editing UX, search/filter features.
 [Sprint] Implement file caching for in-session edits (avoid reload/loss).
Current Goal
Implement Categorise module view/presenter for DB and ad-hoc flows.

