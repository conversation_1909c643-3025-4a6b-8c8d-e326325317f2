# Next Task Prompt:

#  settings icon and setting_pane 

settings pane accessed from settings icon in right_side_bar
will sit under nav panel

icon in 
[link](flatmate/src/fm/gui/icons/settings/selected.svg)

## What does it do?
 it needs to open the right_panel
 and display the settings_pane

 when dev mode is on it will display the dev options
 later, in production made it may show a more limited subset
 - check  [config.py](/flatmate/src/fm/core/config/config.py)
and see that dev_mode is activated. 

## settings_pane
Needs to be designed in such a way that it the modules can put module relevant, ie context specific content in there

the settings pain will use a check box and label base class from core components. eg
[link](/flatmate/src/fm/core/components/lbl_label.py)
we need to make this available to the modules. 
I believe it is in components/in a generically named file.
it is my belief (discuss) that the different widgets and widget groups should be brought out into seperate files.
we will need to fix existing imports that use these base classes.
the widget bass classes should employ the qss hierarchy in gui/styles as should all widgets

# folder structure and naming convention
## discussion needed:
fm/gui/module_ui_components/
-naming convention
lbl_label
chb_check_box
chb_lbl_inline 
chb_lbl_vert
    /panes
    -settings_pane.py
    -nav_pane.py *will break imports
    -options_pane.py
    /panel_managers
       -left_panel_manager.py
       -right_panel_manager.py
       -center_panel_manager.py
       -readme.md
    /side_bars
       -*left_side_bar_manager.py
       -right_side_bar_manager.py
       -readme.md
    ### *will denote not yet implemented, in readme.md

## questions: who uses current nav pane?
<modules or main_window?>
If used by modules, perhaps components should be called module_ui_components ?
gui specific components should be in gui/main_window_components ?
(currently just coded in main_window.py)












# Icon Wrangler ?[x]
[link](/a_dev_tools/icon_helper_scripts/browse_phosphor_icons_optimized.py)

## we will need to make the phosphor icon wrangler 
a little more adaptive:

## so we can put an icon in a collection and save it anywhere


we need to be able to put an icon in to a "collection
(store links to svg files in a json and display those icons in the gui) in a collapsible left side icon_collection_browser, organised by collection names...

right clicking an icon and either add it to a collection, or save it to a new collection, or save it to a new svg file in a folder on your computer, using a save as file dilogue that by default, opens in the workspace folder, and there after in the last used folder. (another json)


## FOR NOW:
