"""Processor wrapper for the Categorize module.

For MVP this forwards statement processing to the existing Update-Data
pipeline via `fm.statement_processing.process_files`, then applies
categorisation.
"""
from typing import List
import pandas as pd

from fm.statement_processing import process_files
from .categorizer import TransactionCategorizer


def categorize_files(file_paths: List[str]) -> pd.DataFrame:
    """Parse, merge and categorise one or more bank-statement files.

    Args:
        file_paths: list of paths to CSV/XLSX statement files.
    Returns:
        DataFrame with an added "category" column.
    """
    df = process_files(file_paths)
    cat = TransactionCategorizer()
    df["category"] = df.apply(cat.categorize_row, axis=1)
    return df
