{
	"folders": [
		{
			"path": "."
		},
		{
			"path": "../../../.flatmate"
		}
	],
	"settings": {
		"files.exclude": {
			"**/.git": true,
			"**/.svn": true,
			"**/.hg": true,
			"**/.DS_Store": true,
			"**/Thumbs.db": true
		},
		"files.autoSave": "afterDelay",
		"explorer.excludeGitIgnore": true,
		"explorerExclude.backup": {}
	}
	}
		// "terminal.integrated.persistentSessionReviveProcess": "onExitAndWindowClose",
		// "python.terminal.activateEnvironment": true,
		// "python.defaultInterpreterPath": "${workspaceFolder}/flatmate/.venv_fm313/Scripts/python.exe"
		
		// Custom terminal profile using the preload script
// 		"terminal.integrated.profiles.windows": {
// 			"Flatmate Git Bash": {
// 				"path": "C:\\Program Files\\Git\\bin\\bash.exe",
// 				"args": ["-c", "cd ${workspaceFolder}/flatmate && source ./scripts/preload_env.sh && exec bash"]
// 			}
// 		},
// 		"terminal.integrated.defaultProfile.windows": "Flatmate Git Bash"
// 	}
// }







