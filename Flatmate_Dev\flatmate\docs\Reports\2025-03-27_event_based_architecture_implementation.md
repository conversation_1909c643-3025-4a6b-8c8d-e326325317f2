# Event-Based Architecture Implementation Guide

This document provides a practical implementation guide for transitioning the FlatMate application to a proper event-based architecture. It builds on the analysis in the in-depth system report and provides concrete code examples and implementation steps.

## Overview

The current navigation system in FlatMate uses a hybrid approach, combining Qt signals with some event bus usage. This creates inconsistency and tight coupling between components. The goal is to standardize on a publish/subscribe event system that provides clean architectural boundaries with proper interfaces between components.

## Implementation Steps

### 1. Create Module Utilities

Create a new file `fm/core/module_utils.py` to define the module transition event structure and utility function:

```python
"""Utilities for module transitions and event-based communication."""

from dataclasses import dataclass
from typing import Dict, Any

from .services.event_bus import Events, global_event_bus


@dataclass
class ModuleTransitionEvent:
    """Event data for module transition requests."""
    module_name: str
    params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.params is None:
            self.params = {}


def request_module_transition(module_name: str, **params: Dict[str, Any]):
    """
    Request a transition to another module.
    
    This function publishes a MODULE_TRANSITION_REQUESTED event
    that will be handled by the ModuleCoordinator.
    
    Args:
        module_name: The name of the module to transition to
        **params: Optional parameters to pass to the module
    """
    event = ModuleTransitionEvent(module_name=module_name, params=params)
    global_event_bus.publish(Events.MODULE_TRANSITION_REQUESTED, event)
    print(f"[module_utils] Published MODULE_TRANSITION_REQUESTED event for module: '{module_name}'")
```

### 2. Update Events Class

Add the module transition event type to `fm/core/services/event_bus.py`:

```python
class Events:
    # Existing events...
    
    # Module transition event
    MODULE_TRANSITION_REQUESTED = 'module_transition_requested'
```

### 3. Update ModuleCoordinator

Modify `fm/module_coordinator.py` to subscribe to module transition events:

```python
def __init__(self, main_window):
    """Initialize the coordinator.
    
    Args:
        main_window: The main window instance that serves as a vessel
    """
    print("\n=== Initializing Module Coordinator ===")
    self.main_window = main_window
    self.current_module = None
    self.module_factories = {}
    
    # Load module preferences using new config
    self.recent_modules = config.get_value(ConfigKeys.Window.RECENT_FILES, default=[])
    
    # Subscribe to module transition events
    from .core.services.event_bus import Events, global_event_bus
    global_event_bus.subscribe(
        Events.MODULE_TRANSITION_REQUESTED, 
        self._handle_transition_request
    )

def _handle_transition_request(self, event):
    """Handle module transition request events."""
    if hasattr(event, 'module_name'):
        module_name = event.module_name
        params = event.params if hasattr(event, 'params') else {}
        self.transition_to(module_name, **params)
    else:
        print(f"Warning: Received invalid module transition event: {event}")
```

### 4. Update NavPane

Modify `fm/gui/_main_window_components/right_side_bar/nav_pane.py` to use the event bus instead of signals:

```python
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QVBoxLayout, QPushButton, QSizePolicy

from ....core.module_utils import request_module_transition
from ....core.services.logger import log
from ...components.styled_widgets import StyledIconButton


class NavPane(QWidget):
    """Navigation pane for the main window."""
    
    # Remove the navigationSelected signal
    # navigationSelected = Signal(str)
    
    def __init__(self, parent=None):
        """Initialize the navigation pane."""
        super().__init__(parent)
        self.setObjectName("navPane")
        
        # Set up the layout
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        self.layout.setAlignment(Qt.AlignTop)
        
        # Navigation buttons
        self.buttons = {}
        self.current_item = None
        
        # Set up the navigation items
        self._setup_navigation()
    
    def _setup_navigation(self):
        """Set up the navigation buttons."""
        # Define navigation items: (module_name, icon_name)
        nav_items = [
            ('home', 'home'),
            ('update_data', 'database'),
            ('settings', 'settings'),
        ]
        
        # Create buttons for each item
        for module_name, icon_name in nav_items:
            button = StyledIconButton(icon_name=icon_name)
            button.setToolTip(module_name.replace('_', ' ').title())
            button.setCheckable(True)
            button.setObjectName(f"nav_{module_name}")
            button.clicked.connect(lambda checked, m=module_name: self._on_button_clicked(m))
            
            self.layout.addWidget(button)
            self.buttons[module_name] = button
    
    def _on_button_clicked(self, module_name):
        """Handle button click event."""
        self._select_item(module_name)
    
    def _select_item(self, item_id):
        """Select a navigation item and request a module transition."""
        self.highlight_item(item_id)
        request_module_transition(item_id)
    
    def highlight_item(self, item_id):
        """Highlight the specified navigation item."""
        # If the item doesn't exist, do nothing
        if item_id not in self.buttons:
            log(f"Warning: No navigation button for '{item_id}'", "w")
            return False
        
        # Uncheck all buttons
        for button in self.buttons.values():
            button.setChecked(False)
        
        # Check the selected button
        self.buttons[item_id].setChecked(True)
        self.current_item = item_id
        return True
```

### 5. Update RightSideBarManager

Modify `fm/gui/_main_window_components/right_side_bar/_right_side_bar_manager.py` to remove signal forwarding:

```python
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QVBoxLayout

from .nav_pane import NavPane


class RightSideBarManager(QWidget):
    """Manager for the right side bar components."""
    
    # Remove the navigationSelected signal
    # navigationSelected = Signal(str)
    
    def __init__(self, parent=None):
        """Initialize the right side bar manager."""
        super().__init__(parent)
        self.setObjectName("rightSideBar")
        
        # Set up the layout
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        
        # Create the navigation pane
        self.nav_pane = NavPane(self)
        self.layout.addWidget(self.nav_pane)
        
        # No need to connect to navigation signals anymore
        # self.nav_pane.navigationSelected.connect(self._on_navigation_selected)
    
    # Remove the _on_navigation_selected method
    # def _on_navigation_selected(self, item_id):
    #     self.navigationSelected.emit(item_id)
```

### 6. Update MainWindow

Modify `fm/gui/main_window.py` to remove signal connections to the ModuleCoordinator:

```python
def set_module_manager(self, coordinator):
    """Set the module manager for the main window.
    
    Args:
        coordinator: The module coordinator instance
    """
    self.module_coordinator = coordinator
    
    # No need to connect signals anymore
    # self.right_side_bar_manager.navigationSelected.connect(coordinator.transition_to)
```

### 7. Update HomePresenter

Modify `fm/modules/home/<USER>

```python
def _connect_signals(self):
    """Connect view signals to handlers."""
    print("Connecting Home View signals")
    
    from ...core.module_utils import request_module_transition
    
    # Use the standard module transition request function
    self.view.update_data_clicked.connect(
        lambda: request_module_transition("update_data")
    )
    self.view.settings_clicked.connect(
        lambda: request_module_transition("settings")
    )
    self.view.quit_clicked.connect(self._on_quit_click)
```

### 8. Update UpdateDataPresenter

Modify `fm/modules/update_data/ud_presenter.py` to use the request_module_transition function:

```python
def _connect_signals(self):
    """Connect view signals to handlers."""
    from ...core.module_utils import request_module_transition
    
    self.view.cancel_clicked.connect(
        lambda: request_module_transition("home")
    )
    # ... rest of the method ...
```

## Benefits of This Implementation

1. **Clean Architectural Boundaries**: Components communicate through well-defined interfaces.
2. **Consistent Terminology**: Standardizes on "publish/subscribe" terminology.
3. **Reduced Coupling**: Components don't need direct references to each other.
4. **Simplified Navigation**: All module transitions use the same mechanism.
5. **Improved Testability**: Components can be tested in isolation.
6. **Easier Debugging**: Consistent event flow makes debugging simpler.

## Testing the Implementation

After implementing these changes, test the application thoroughly:

1. Test navigation from the NavPane to different modules
2. Test navigation from buttons within modules
3. Test navigation with parameters (if applicable)
4. Test error handling for invalid module names

## Future Enhancements

Once the basic event-based architecture is in place, consider these enhancements:

1. **Event Logging**: Add comprehensive logging of events for debugging
2. **Event Replay**: Implement event replay for testing
3. **Event Filtering**: Add the ability to filter events by type or source
4. **Event Prioritization**: Implement priority levels for different event types

## Conclusion

This implementation guide provides a practical approach to transitioning the FlatMate application to a proper event-based architecture. By following these steps, you'll create a more maintainable, testable, and extensible codebase that follows best practices for event-driven systems.
