# Refactoring Examples for Update Data Module

class UpdateDataPresenter_Before:
    """
    Original presenter implementation with mixed responsibilities
    and less clear state management
    """
    def __init__(self, view, config_manager):
        self._view = view
        self._config_manager = config_manager
        
        # Directly managing view state without clear separation
        self._view.process_button.set_enabled(False)
    
    def _update_job_sheet(self, source_path=None, save_path=None):
        # Mixing state logic with view updates
        if source_path and save_path:
            self._view.process_button.set_enabled(True)
        else:
            self._view.process_button.set_enabled(False)


class UpdateDataPresenter_After:
    """
    Refactored presenter with clearer state management
    and separation of concerns
    """
    def __init__(self, state_module, view_factory):
        """
        Initialize with dedicated state module and view factory
        
        Args:
            state_module (UpdateDataState): Determines initial state
            view_factory: Creates view with initial configuration
        """
        self._state_module = state_module
        
        # Get initial configuration from state module
        initial_config = self._state_module.get_initial_view_config()
        
        # Create view with predefined configuration
        self._view = view_factory.create(initial_config)
    
    def _update_job_sheet(self, source_path=None, save_path=None):
        """
        Update job sheet with clear state validation
        
        Args:
            source_path (str, optional): Selected source path
            save_path (str, optional): Selected save path
        """
        # Clear separation of state determination
        is_valid = bool(source_path and save_path)
        
        # Delegate state updates to view
        self._view.set_process_button_state(is_valid)


class UpdateDataView_Before:
    """
    Original view with mixed error handling and state management
    """
    def __init__(self, config_manager):
        try:
            # Direct error handling in view
            master_path = config_manager.get_master_path()
            self._setup_widgets(master_path)
        except FileNotFoundError:
            # User-facing error in view
            self._show_error_dialog("Master file not found")


class UpdateDataView_After:
    """
    Refactored view with passive configuration
    """
    def __init__(self, initial_config):
        """
        Initialize view with predefined configuration
        
        Args:
            initial_config (dict): Configuration from state module
        """
        self._process_button_enabled = initial_config.get('process_button_enabled', False)
        self._mode = initial_config.get('mode', 'default')
        
        # Defer widget creation based on configuration
        self._setup_widgets()
    
    def set_process_button_state(self, enabled):
        """
        Update process button state
        
        Args:
            enabled (bool): Whether process button should be enabled
        """
        self._process_button.set_enabled(enabled)


# Demonstration of State Module Integration
class UpdateDataStateModule:
    """
    Centralized state determination for Update Data module
    """
    def __init__(self, config_manager, global_state):
        self._config_manager = config_manager
        self._global_state = global_state
    
    def get_initial_view_config(self):
        """
        Determine initial view configuration
        
        Returns:
            dict: Minimal configuration for view initialization
        """
        return {
            'process_button_enabled': self._determine_process_button_state(),
            'mode': self._determine_module_mode(),
            'has_master_file': self._check_master_file_existence()
        }
    
    def _determine_process_button_state(self):
        """
        Complex logic for determining process button state
        
        Returns:
            bool: Whether process button should be enabled
        """
        # Example complex state determination
        return (
            self._check_master_file_existence() and
            self._global_state.is_system_ready()
        )
    
    def _check_master_file_existence(self):
        """
        Check master file existence
        
        Returns:
            bool: Master file existence status
        """
        return self._config_manager.master_exists()
    
    def _determine_module_mode(self):
        """
        Determine initial module operational mode
        
        Returns:
            str: Module's initial mode
        """
        if self._check_master_file_existence():
            return 'view_only'
        return 'default'
