{"level": "DEBUG", "module": "update_data.config", "message": "Configuration initialized with default settings"}
{"level": "DEBUG", "module": "fm.modules.update_data.services.local_services", "message": "UpdateDataServices initialized"}
{"level": "INFO", "module": "fm.core.services.logger", "message": "=== FlatMate Application Starting ==="}
{"level": "INFO", "module": "fm.core.services.logger", "message": "Python version: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]"}
{"level": "INFO", "module": "fm.core.services.logger", "message": "Log file: C:\\Users\\<USER>\\.flatmate\\logs\\flatmate.log"}
{"level": "INFO", "module": "fm.core.services.logger", "message": "Log level: DEBUG"}
{"level": "DEBUG", "module": "fm.core.services.logger", "message": "Ensured directory exists: C:\\Users\\<USER>\\_DEV\\__PROJECTS\\Flatmate_Dev\\flatmate\\src\\data"}
{"level": "DEBUG", "module": "fm.core.services.logger", "message": "Ensured directory exists: C:\\Users\\<USER>\\_DEV\\__PROJECTS\\Flatmate_Dev\\flatmate\\src\\logs"}
{"level": "DEBUG", "module": "fm.core.services.logger", "message": "Ensured directory exists: C:\\Users\\<USER>\\_DEV\\__PROJECTS\\Flatmate_Dev\\flatmate\\src\\config"}
{"level": "DEBUG", "module": "fm.core.services.logger", "message": "Ensured directory exists: C:\\Users\\<USER>\\_DEV\\__PROJECTS\\Flatmate_Dev\\flatmate\\src\\cache"}
{"level": "INFO", "module": "fm.core.services.logger", "message": "\n=== Setting up Module Coordinator ==="}
{"level": "INFO", "module": "fm.core.services.logger", "message": "Initializing Module Coordinator"}
{"level": "DEBUG", "module": "fm.core.services.logger", "message": "Loaded recent modules: []"}
{"level": "INFO", "module": "fm.core.services.logger", "message": "Initializing Module Factories"}
{"level": "DEBUG", "module": "fm.core.services.logger", "message": "Registered module factories: ['home', 'update_data']"}
