# Categorise Module – MVP Overview

_Last updated: 2025-06-16_

## 1. Objective
Deliver a minimum-viable Categorise module that lets users:
1. Load transactions (from DB or ad-hoc CSV/XLSX) with optional date-range & account filters.
2. View, search, filter, and tag (`tags` column) transactions in a modern, ergonomic table.
3. Persist tag edits back to the database safely (canonical columns remain read-only).
4. Manage categorisation patterns/rules via a simple UI.
5. Navigate between modules without losing unsaved edits (in-session caching).

## 2. Architecture Snapshot
* **GUI Shell** – Vessel GUI; modules inherit from `BaseModuleView`.
* **Data Access** – `DataService` facade over SQLite; will gain date/account filters & `tags` column.
* **Processing Layer** – Statement handlers + `TransactionCategorizer` accessed through shim `fm.statement_processing.process_files`.
* **Caching** – new `fm.core.cache.CacheService` (in-memory + optional temp-file spill).

```
[DB] ⇄ DataService ⇄ Presenter ⇄ CatView (QTableView)
               ⇅
        CacheService (edited DF)
```

## 3. UI/UX Requirements
* **Table Widget** – `QTableView` + `QSortFilterProxyModel`.
  * Sortable (default: date desc), resizable, filterable.
  * Per-column hide/show (required cols visible by default).
  * Read-only canonical columns; editable `tags` column (comma-separated, multi-word).
  * "⚙" utility column per row (quick-set tag, add pattern, mark reviewed – bulk-action aware).
* **Controls** – date-range picker, account dropdown, filter row, column visibility toggle.
* **Style** – adopt Update-Data button style (`select_btn`, `exit_btn`); refresh Qt stylesheet for modern look; consider top menu in v2.

## 4. Data Integrity Rules
* Canonical cols (`id`, `date`, `amount`, `description`, etc.) are **read-only**.
* Tags are stored in new `tags` column; string of comma-separated values.
* Validation blocks any attempt to modify canonical data on save.

## 5. Caching Strategy
* Presenter keeps edited DataFrame in memory.
* On `cleanup()` it passes DF to `CacheService.put(module_key, df)`.
* On `initialize()` it attempts `CacheService.get(...)` before querying DB.
* Cache can persist to temp-file for large DF; LRU/time-based expiry.

## 6. Pattern Management
* JSON file of regex ➜ tag rules.
* Dialog (accessible from toolbar or ⚙ utility column) to add/edit/delete rules.
* Changes immediately affect in-memory categorisation; persisted on disk.

## 7. Open Questions
1. Undo/redo for tag changes?
2. Export filtered view (CSV/XLSX)?
3. Multi-user conflict handling (out of scope for MVP)?

---

## 8. Actionable Task List (MVP Sprint)

### 1. Core Back-End
- [ ] Extend `DataService.get_transactions(start_date, end_date, account_id)`
- [ ] Add `tags` column to transactions table and ORM/dataclass
- [ ] Write DB migration script

### 2. Caching Service
- [ ] Implement `fm.core.cache.CacheService`
- [ ] Integrate caching in Categorise presenter

### 3. UI Enhancements
- [ ] Insert date-range picker & account dropdown
- [ ] Make canonical cols read-only; replace category with editable `tags` col
- [ ] Implement "⚙" utility column with dropdown actions
- [ ] Column visibility toggle & filter row
- [ ] Default sort: date desc; ensure columns resizable

### 4. Save & Validation Workflow
- [ ] “Save Tags” action writing only `tags` column back to DB
- [ ] Prompt on unsaved changes when navigating away

### 5. Pattern Editing UI
- [ ] Build regex➜tag rule dialog; load/save JSON

### 6. Docs & Clean-up
- [ ] Update `ergonomics_notes.md` as decisions evolve
- [ ] Keep this checklist in sync; tick items on completion
