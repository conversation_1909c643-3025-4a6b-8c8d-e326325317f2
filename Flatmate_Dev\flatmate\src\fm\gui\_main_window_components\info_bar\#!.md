#!.md

# info bar levereages the events system to display messages.

any module can send a message to the info bar.

it currently is positioned at the bottom of the center panel, and is currently only utilised by ud_data module.

it is a QStatusBar that is updated with the message text. or it least, it was supposed to be.
f knows where that was created... ask the ai.

It should be implemented in main window with a show hide method
(service always on? started in main window at init?)