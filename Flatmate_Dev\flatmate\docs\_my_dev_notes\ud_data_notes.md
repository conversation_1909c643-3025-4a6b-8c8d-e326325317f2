- select source should revert to last used preference
- opening to display the last saved master/merged  csv (when present at last known locaton - see master file tracker) is not optimal
- no feed back on processing... look into current center panel widgets and director/ presenter
- note consider status bar updates "found kiwibank basic csv" - so fast you wouldnt see them - perhaps catch messages- store and display after processing or just add to text widget - eg "processing info widget"