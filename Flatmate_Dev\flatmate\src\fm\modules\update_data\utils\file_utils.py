"""
Utility functions for file operations in the Update Data module.
"""

import os
from typing import Optional

import pandas as pd

from fm.core.services.logger import log


def load_csv_to_df(filepath: str) -> Optional[pd.DataFrame]:
    """
    Load a CSV file with consistent metadata.
    
    Args:
        filepath: Path to the CSV file
        
    Returns:
        DataFrame with attached metadata or None if loading fails
    """
    if not os.path.exists(filepath) or not os.path.isfile(filepath):
        log(f"File does not exist: {filepath}", level="error")
        return None
        
    try:
        # Read the CSV file
        df = pd.read_csv(filepath)
        
        # Store metadata in attrs (recommended approach)
        df.attrs['filepath'] = filepath
        df.attrs['filename'] = os.path.basename(filepath)
        
        # For backward compatibility
        df.filepath = filepath
        df.filename = os.path.basename(filepath)
        
        return df
        
    except Exception as e:
        log(f"Error loading {filepath}: {str(e)}", level="error")
        return None
