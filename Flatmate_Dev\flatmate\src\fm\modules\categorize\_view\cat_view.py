"""Main view for the Categorize module."""

import pandas as pd
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QFileDialog
from fm.core.services.logger import Logger

from fm.modules.base.base_module_view import BaseModuleView
from fm.core.services.event_bus import global_event_bus
from .components.left_panel._panel_coordinator import LeftPanelManager
from .components.center_panel._panel_coordinator import CenterPanelCoordinator



class CatView(BaseModuleView):
    """Main view for the Categorize module."""
    
    # Signals
    cancel_clicked = Signal()
    files_selected = Signal(list)
    load_db_requested = Signal(dict)
    transaction_selected = Signal(int)
    tags_updated = Signal(int, str)
    
    def __init__(self, parent=None):
        self.logger = Logger()
        # Initialize basic properties first
        self.event_bus = global_event_bus
        # Then call parent constructor which will call setup_ui
        super().__init__(parent)
        self.setObjectName("CatView")
    
    def setup_ui(self):
        """Initial UI setup - called by base class."""
        # Create panel managers
        self.left_panel_manager = LeftPanelManager()
        self.center_panel_manager = CenterPanelCoordinator()
        
        # Connect signals
        self._connect_signals()
    
    def _connect_signals(self):
        """Connect internal signals to be forwarded."""
        # Connect left panel signals
        self.left_panel_manager.files_select_requested.connect(self._on_select_files)
        self.left_panel_manager.load_db_requested.connect(self._on_load_db)
        self.left_panel_manager.apply_filters_clicked.connect(self.load_db_requested)
        self.left_panel_manager.cancel_clicked.connect(self.cancel_clicked)
        
        # Connect center panel signals
        self.center_panel_manager.transaction_selected.connect(self.transaction_selected)
        self.center_panel_manager.tags_updated.connect(self.tags_updated)
    
    def setup_left_panel(self, layout):
        """Set up the left panel with filters and options."""
        layout.addWidget(self.left_panel_manager)
    
    def setup_center_panel(self, layout):
        """Set up the center panel with the transaction table."""
        # The TransactionViewPanel already includes the top bar with filtering
        # No need for a separate top bar - it's built into the EnhancedTableWidget
        layout.addWidget(self.center_panel_manager)
    
    def set_dataframe(self, df: pd.DataFrame):
        """Set the transactions dataframe to display."""
        self.logger.debug(f"CatView setting dataframe: {len(df) if df is not None else 0} rows")
        if df is not None and not df.empty:
            self.logger.debug(f"DataFrame columns: {df.columns.tolist()}")
            self.center_panel_manager.set_transactions(df)
    
    def _on_select_files(self):
        """Handle select files button click."""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "Select Bank Statement Files",
            "",
            "CSV Files (*.csv);;Excel Files (*.xlsx *.xls);;All Files (*.*)"
        )
        
        if file_paths:
            self.files_selected.emit(file_paths)
    
    def _on_load_db(self):
        """Handle load from database button click."""
        filters = self.left_panel_manager.get_filters()
        self.load_db_requested.emit(filters)
    
    def set_accounts(self, accounts):
        """Set the available accounts in the combo box."""
        self.left_panel_manager.set_accounts(accounts)
    
    def disconnect_signals(self):
        """Clean up signal connections."""
        # Disconnect left panel signals
        if hasattr(self, 'left_panel_manager'):
            try:
                self.left_panel_manager.files_select_requested.disconnect(self._on_select_files)
                self.left_panel_manager.load_db_requested.disconnect(self._on_load_db)
                self.left_panel_manager.apply_filters_clicked.disconnect(self.load_db_requested)
                self.left_panel_manager.cancel_clicked.disconnect(self.cancel_clicked)
                self.left_panel_manager.disconnect_signals()
            except (TypeError, RuntimeError):
                # Signal might not be connected
                pass
        
        # Disconnect center panel signals
        if hasattr(self, 'center_panel_manager'):
            try:
                self.center_panel_manager.transaction_selected.disconnect(self.transaction_selected)
                self.center_panel_manager.tags_updated.disconnect(self.tags_updated)
                self.center_panel_manager.disconnect_signals()
            except (TypeError, RuntimeError):
                # Signal might not be connected
                pass
    
    def cleanup(self):
        """Clean up resources."""
        self.disconnect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        super()._init_ui()
        
        # Debug center panel initialization
        self.logger.debug("Initializing CatView UI components")
        
        # Check if center_panel_manager is properly initialized
        if hasattr(self, 'center_panel_manager'):
            self.logger.debug(f"Center panel manager type: {type(self.center_panel_manager).__name__}")
            if hasattr(self.center_panel_manager, 'center_panel'):
                self.logger.debug(f"Center panel type: {type(self.center_panel_manager.center_panel).__name__}")
                if hasattr(self.center_panel_manager.center_panel, 'transaction_table'):
                    self.logger.debug(f"Transaction table type: {type(self.center_panel_manager.center_panel.transaction_table).__name__}")
        else:
            self.logger.error("Center panel manager not initialized")





