Category,Config Type,Module,Parameter,Type,Default,Required,Description,Source File,Restart Required,Scope
env,init,app,env.mode,str,development,yes,Environment mode (dev/prod),config/env.py,yes,system
env,init,app,env.debug,bool,FALSE,no,Show debug information,config/env.py,yes,system
env,init,app,env.os_type,str,auto-detect,yes,Operating system (windows/mac/linux),config/env.py,yes,system

# Core System Paths
sys,init,app,paths.project_root,Path,.,yes,Project root directory,core/config/paths.py,yes,system
sys,init,app,paths.logs,Path,PROJECT_ROOT/logs,yes,Log files directory,core/config/paths.py,yes,system
sys,init,app,paths.config,Path,PROJECT_ROOT/configs,yes,Config files directory,core/config/paths.py,yes,system
sys,init,app,paths.resources,Path,PROJECT_ROOT/resources,yes,Resources directory,core/config/paths.py,yes,system

# User Data and Profile Paths
sys,init,app,paths.user_home,Path,~/.flatmate,yes,User flatmate directory,core/config/paths.py,yes,system
sys,init,app,paths.data,Path,~/.flatmate/data,yes,User data directory,core/config/paths.py,yes,system
sys,init,app,paths.profiles,Path,~/.flatmate/profiles,yes,Profile storage directory,core/config/paths.py,yes,system

# Logging
log,runtime,app,log.show_info,bool,TRUE,no,Show info messages,core/logger.py,no,user
log,runtime,app,log.show_warnings,bool,TRUE,no,Show warning messages,core/logger.py,no,user

# Update Data Module
sys,init,update_data,paths.master,Path,${paths.data}/master,yes,Master CSV location,modules/update_data/config/ud_config.py,yes,system
sys,init,update_data,paths.backup,Path,${paths.data}/backup,yes,Backup directory,modules/update_data/config/ud_config.py,yes,system
sys,runtime,update_data,paths.recent_masters,list,[],no,List of recently used master files,modules/update_data/config/ud_config.py,no,user
sys,runtime,update_data,paths.master_history,dict,{},no,History of master files and their sources,modules/update_data/config/ud_config.py,no,user

# UI Settings
ui,runtime,app,window.left_panel_width,int,250,no,Left panel width,gui/main_window.py,no,user
ui,runtime,app,window.right_panel_width,int,250,no,Right panel width,gui/main_window.py,no,user
ui,runtime,app,window.column_widths,dict,{},no,Table column widths by table ID,gui/main_window.py,no,user
ui,runtime,app,window.recent_files,list,[],no,Recently opened files,gui/main_window.py,no,user

# Theme Constants
const,init,app,theme.colors.primary,str,#3B8A45,yes,Primary theme color,gui/styles/constants.py,yes,const
const,init,app,theme.colors.background,str,#2C2C2C,yes,Background color,gui/styles/constants.py,yes,const
const,init,app,theme.font.size,int,12,yes,Base font size (requires app restart),gui/styles/constants.py,yes,const

# Data Format Constants
const,init,update_data,formats.date_format,str,%d/%m/%Y,yes,Bank date format,modules/update_data/utils/formatting/formats/bank_format.py,yes,const
