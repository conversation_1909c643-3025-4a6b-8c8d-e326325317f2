# Database Integration Refactoring Task

## Current State Analysis

### Central Database Implementation Document
The central database implementation document outlines the architecture and components of the database system:

- **Architecture**: Clean boundaries with proper interfaces between components
- **Components**: Data Models, Repositories, Services, and Converters
- **Integration with Update Data Module**: The Update Data module can save processed transactions to both:
  - A master CSV file (with dates in UK format: DD-MM-YY)
  - The central database (with dates in ISO format: YYYY-MM-DD)
- **Implementation Details**: Currently, `dw_pipeline.py` handles both CSV saving and database updates

### Current Pipeline Implementation
The current pipeline file (`dw_pipeline.py`) has been modified to include database integration:
- Added imports for data service and converter
- Added functions for database updates:
  - `update_database_from_df()`
  - `update_database_records()`
- The pipeline now handles both saving to CSV and updating the database

### Current Director Implementation
The director file (`dw_director.py`) has been updated to:
- Import the `update_database_records` function from the pipeline
- Accept an `update_database` flag in the job sheet
- Call the database update function when the flag is set




## Task Plan

1. **Revert Pipeline**: 
   - Rename current `dw_pipeline.py` to `dw_pipeline.py.bak`
   - Restore the pre-database integration version from backup

2. **Update Director**:
   - Modify `dw_director.py` to handle database integration
   - Move database update logic from pipeline to director
   - Maintain clean architectural boundaries

3. **Reintegrate Database**:
   - Ensure proper imports in the director
   - Add database update functionality to the director
   - Maintain separation of concerns

## Benefits of This Approach

- **Cleaner Architecture**: The pipeline focuses solely on data processing and CSV generation
- **Proper Separation of Concerns**: The director orchestrates both CSV saving and database updates
- **Maintainability**: Easier to maintain and extend each component independently


## consider a "not processed" folder.
containing, unrecognised, and invalid files.
It would be helpful to attach the reason or associated error in some way.. perhaps create a simple text file, with the same file name appened with "_issues" I'm not sure.
This is low priority currently, we are focusing on the database and core funcitonality

