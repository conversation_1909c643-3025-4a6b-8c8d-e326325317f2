"""Test the tags column migration script."""
import os
import tempfile
import unittest
import sqlite3
from pathlib import Path

# Import the migration script
from fm.database_service.migrations.update_schema_consistency import migrate_database

class TestTagsMigration(unittest.TestCase):
    """Test cases for the tags column migration."""
    
    def setUp(self):
        """Create a temporary test database."""
        # Create a temporary file for the test database
        self.temp_db_fd, self.temp_db_path = tempfile.mkstemp(suffix='.db')
        self.db_path = Path(self.temp_db_path)
        
        # Create a connection to the database
        self.conn = sqlite3.connect(self.temp_db_path)
        self.cursor = self.conn.cursor()
        
        # Create a simple transactions table without the tags column
        self.cursor.execute('''
            CREATE TABLE transactions (
                id INTEGER PRIMARY KEY,
                date TEXT,
                description TEXT,
                amount REAL,
                account_number TEXT,
                is_deleted INTEGER DEFAULT 0
            )
        ''')
        
        # Insert some test data
        self.cursor.execute('''
            INSERT INTO transactions (date, description, amount, account_number)
            VALUES ('2023-01-01', 'Test Transaction', 100.0, '123456')
        ''')
        
        self.conn.commit()
    
    def tearDown(self):
        """Clean up the test database."""
        self.conn.close()
        os.close(self.temp_db_fd)
        os.unlink(self.temp_db_path)
    
    def test_migration_adds_tags_column(self):
        """Test that the migration adds the tags column."""
        # Run the migration
        success = migrate_database(self.db_path)
        
        # Check that the migration was successful
        self.assertTrue(success)
        
        # Check that the tags column exists
        self.cursor.execute("PRAGMA table_info(transactions)")
        columns = [info[1] for info in self.cursor.fetchall()]
        self.assertIn("tags", columns)
        
        # Check that the default value was applied
        self.cursor.execute("SELECT tags FROM transactions WHERE id = 1")
        tags_value = self.cursor.fetchone()[0]
        self.assertEqual(tags_value, "")
    
    def test_migration_idempotent(self):
        """Test that running the migration twice doesn't cause errors."""
        # Run the migration twice
        first_run = migrate_database(self.db_path)
        second_run = migrate_database(self.db_path)
        
        # Both runs should be successful
        self.assertTrue(first_run)
        self.assertTrue(second_run)
        
        # The tags column should still exist
        self.cursor.execute("PRAGMA table_info(transactions)")
        columns = [info[1] for info in self.cursor.fetchall()]
        self.assertIn("tags", columns)

if __name__ == "__main__":
    unittest.main()
