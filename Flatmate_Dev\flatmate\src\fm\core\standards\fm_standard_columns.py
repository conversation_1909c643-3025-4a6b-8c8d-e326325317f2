from enum import Enum


class StandardColumns(Enum):
    """
    Standardized column names for bank transactions with both display and database names
    """
    # Core Transaction Identifiers
    DATE = 'Date'
    DETAILS = 'Details'
    AMOUNT = 'Amount'
    BALANCE = 'Balance'
    ACCOUNT = 'Account'
    
    # Unique Transaction Identifier (if present in source)
    UNIQUE_ID = 'Unique Id' 
    
    # Amount Columns
    CREDIT_AMOUNT = 'Credit'
    DEBIT_AMOUNT = 'Debit'

    # Source Info
    SOURCE_FILENAME = 'Source Filename'
    
    # This Party (TP) Details - Account Holder's Side
    PAYMENT_TYPE = 'Payment Type'
    TP_REF = 'TP Ref'
    TP_PART = 'TP Part'
    TP_CODE = 'TP Code'

    # Other Party (OP) Details - Counterparty Side
    OP_REF = 'OP Ref'
    OP_PART = 'OP Part'
    OP_CODE = 'OP Code'
    OP_NAME = 'OP Name'
    OP_ACCOUNT = 'OP Account'
    
    EMPTY_COLUMN = 'Empty'
    
    @property
    def db_name(self):
        """
        Get the database-friendly column name
        
        Simply converts the enum name to lowercase for consistency.
        """
        return self.name.lower()
    
    @classmethod
    def get_db_column_mapping(cls):
        """
        Get a dictionary mapping display names to database column names
        Useful for SQL queries and DataFrame operations
        """
        return {col.value: col.db_name for col in cls}
    
    @classmethod
    def from_display_name(cls, display_name):
        """
        Get the enum member from a display name

        Args:
            display_name: The display name to look up

        Returns:
            The matching enum member or None if not found
        """
        for col in cls:
            if col.value == display_name:
                return col
        return None

    @classmethod
    def get_standard_column_widths(cls):
        """
        Get standard column widths for consistent UI display.

        Returns:
            Dictionary mapping display names to character widths
        """
        # ? should this be n a seperate utility function and be configurable (rmember last used- add a reset option somewhere)
        return {
            cls.DATE.value: 12,
            cls.DETAILS.value: 40,  # Main content column - wider
            cls.AMOUNT.value: 12,
            cls.ACCOUNT.value: 15,
            cls.BALANCE.value: 12,
            cls.PAYMENT_TYPE.value: 15,
            cls.SOURCE_FILENAME.value: 20,
            # Additional columns not in StandardColumns yet
            'Tags': 20,
            'Category': 20,
            'Notes': 30
        }
        

    @classmethod
    def get_core_columns_in_order(cls):
        """
        Get the core transaction columns in standard display order.

        Returns:
            List of (db_name, display_name) tuples in standard order
        """
        # ? a convenience function..default order should be gleanable form standard enum .. modules should define their own convenience functions or should be in a shared utility somewhere... display_helpers ? 
        
        core_order = [
            cls.DATE,
            cls.DETAILS,
            cls.AMOUNT,
            cls.ACCOUNT,
        ] 
        return [(col.db_name, col.value) for col in core_order]


# For backward compatibility
FmColumnFormat = StandardColumns
