# Proposed Home Module State Enhancement

from typing import Dict, Any, Optional
from src.flatmate.config.config_manager import config
from src.flatmate.models.user_profile import UserProfile


class EnhancedHomeState:
    """
    Proposed Enhanced State Management for Home Module
    
    Key Improvements:
    - Comprehensive view configuration
    - Dynamic module access control
    - Richer state tracking
    - Flexible initialization
    """
    
    def __init__(self, user_profile: Optional[UserProfile] = None):
        """
        Initialize enhanced home state.
        
        Args:
            user_profile (Optional[UserProfile]): Current user profile
        """
        self._user_profile = user_profile or config.get_current_profile()
        self._is_first_run = config.get('app.is_first_run', True)
        self._current_module = None
        self._recent_modules = []
    
    def get_initial_view_config(self) -> Dict[str, Any]:
        """
        Generate comprehensive view configuration.
        
        Returns:
            Dict with detailed view parameters
        """
        return {
            'user_name': self._get_user_name(),
            'is_first_time_user': self._is_first_run,
            'available_modules': self._get_available_modules(),
            'recent_modules': self._get_recent_modules(),
            'welcome_content': self._get_welcome_content(),
            'ui_theme': self._determine_ui_theme()
        }
    
    def _get_user_name(self) -> str:
        """Get user's display name."""
        return self._user_profile.name if self._user_profile else 'Guest'
    
    def _get_available_modules(self) -> list:
        """
        Determine accessible modules based on user profile.
        
        Returns:
            List of module names user can access
        """
        base_modules = ['update_data', 'view_data', 'settings']
        
        if self._user_profile:
            if self._is_first_run:
                # Prioritize onboarding and setup for first-time users
                base_modules.extend(['onboarding', 'profile_setup'])
        
        return base_modules
    
    def _get_recent_modules(self) -> list:
        """
        Retrieve recently accessed modules.
        
        Returns:
            List of recently accessed module names
        """
        return self._recent_modules[-5:]  # Last 5 modules
    
    def _get_welcome_content(self) -> Dict[str, Any]:
        """
        Generate dynamic welcome content.
        
        Returns:
            Dict with welcome content configuration
        """
        from src.flatmate.home.home_content import WELCOME_CONTENT
        
        # Customize content based on first-time status
        if self._is_first_run:
            WELCOME_CONTENT['subtitle'] = self._get_first_run_subtitle()
        
        return WELCOME_CONTENT
    
    def _get_first_run_subtitle(self) -> str:
        """
        Generate first-run subtitle.
        
        Returns:
            Personalized first-run subtitle
        """
        return "Let's set up your Flatmate experience!"
    
    def _determine_ui_theme(self) -> str:
        """
        Determine UI theme based on user preferences.
        
        Returns:
            Theme name (e.g., 'light', 'dark', 'system')
        """
        return config.get('ui.theme', 'system')
    
    def record_module_transition(self, module_name: str):
        """
        Track module access history.
        
        Args:
            module_name (str): Accessed module name
        """
        if module_name not in self._recent_modules:
            self._recent_modules.append(module_name)
            self._current_module = module_name


# Example Usage
def demonstrate_enhanced_state():
    """
    Demonstrate the capabilities of the enhanced home state.
    """
    # Simulate different user scenarios
    first_time_profile = UserProfile(name='New User')
    existing_profile = UserProfile(name='Existing User')
    
    # Create states with different profiles
    first_time_state = EnhancedHomeState(first_time_profile)
    existing_state = EnhancedHomeState(existing_profile)
    
    # Compare view configurations
    print("First-Time User View Config:", first_time_state.get_initial_view_config())
    print("Existing User View Config:", existing_state.get_initial_view_config())
