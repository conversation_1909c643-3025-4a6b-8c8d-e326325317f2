#!/usr/bin/env python3
"""
Tests for the TestDataManager class.
"""

import pytest
from pathlib import Path
from .test_data_manager import TestDataManager

# Sample file paths - update these to point to actual test files in your project
SAMPLE_FILES = [
    Path(__file__).parent / 'test_home.py',
    Path(__file__).parent / 'test_update_data.py'
]

def test_copy_files():
    """Test copying of files."""
    with TestDataManager() as tdm:
        # Copy test files
        copied_files = tdm.copy_files(SAMPLE_FILES)
        
        # Verify files were copied
        assert len(copied_files) == len(SAMPLE_FILES)
        for file in copied_files:
            assert file.exists()
            
            # Verify file contents match original
            original = next(s for s in SAMPLE_FILES if s.name == file.name)
            with open(original, 'rb') as f1, open(file, 'rb') as f2:
                assert f1.read() == f2.read()

def test_backup_verification():
    """Test backup file verification."""
    with TestDataManager() as tdm:
        # Copy source files
        test_files = tdm.copy_files(SAMPLE_FILES)
        
        # Create backup directory and copy files
        backup_dir = tdm.create_backup_dir()
        for source in test_files:
            backup = backup_dir / source.name
            backup.write_bytes(source.read_bytes())
        
        # Verify backups
        assert tdm.verify_backup_files(test_files, backup_dir)

def test_cleanup():
    """Test cleanup of test files."""
    tdm = TestDataManager()
    test_dir = tdm.test_data_dir
    
    # Copy some files
    tdm.copy_files(SAMPLE_FILES)
    
    # Verify directory exists
    assert test_dir.exists()
    
    # Clean up
    tdm.cleanup()
    
    # Verify directory was removed
    assert not test_dir.exists()

def test_context_manager():
    """Test context manager functionality."""
    test_dir = None
    
    # Use context manager
    with TestDataManager() as tdm:
        test_dir = tdm.test_data_dir
        tdm.copy_files(SAMPLE_FILES)
        assert test_dir.exists()
    
    # Verify cleanup after context
    assert not test_dir.exists()
