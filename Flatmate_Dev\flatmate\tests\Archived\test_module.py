"""
Test module to verify base class functionality.
"""

from PySide6.QtWidgets import QLabel, QVBoxLayout, QWidget
from PySide6.QtCore import Qt
from .base.base_module_view import BaseModuleView

class TestModuleView(BaseModuleView):
    """Simple test module view."""
    
    def setup_ui(self):
        """Set up the user interface."""
        # Create content area
        self.content_area = QWidget()
        self.content_area.setObjectName("test_module_content")
        layout = QVBoxLayout(self.content_area)
        
        # Add a simple label
        label = QLabel("Test Module Content")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
    
    def setup_left_panel(self, layout):
        """Set up the left panel."""
        # Add a simple label to left panel
        label = QLabel("Test Module")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
    
    def disconnect_signals(self):
        """Clean up signal connections."""
        # No signals to disconnect in this test module
        pass
