# InfoBar Architecture

## Current Architecture

1. **Main Window**:
   - Creates the InfoBar widget and adds it to the layout
   - Initializes the InfoBarService singleton
   - Connects the InfoBarService signals to the InfoBar widget methods

2. **InfoBarService**:
   - Implemented as a singleton service
   - Uses Qt signals (message_signal, visibility_signal) internally
   - Provides a pub/sub interface with methods like publish_message()

3. **Module Integration**:
   - Each module presenter (like UpdateDataPresenter) gets a reference to the InfoBarService
   - Presenters call methods on the InfoBarService to show messages

4. **Module Coordinator**:
   - Manages transitions between modules
   - Doesn't directly interact with the InfoBar

## Architectural Issues

The current implementation has a few issues:

1. **Tight Coupling**: Modules are directly accessing the InfoBarService, creating a dependency
2. **Inconsistent Interfaces**: Some modules might use the InfoBar directly, others through the service
3. **Unclear Ownership**: The InfoBar is owned by the main window, but used by modules
4. **Mixed Terminology**: The InfoBarService uses both signal/emit and publish/subscribe terminology

## Recommended Approach

To maintain clean architectural boundaries, we recommend:

1. **Consistent Pub/Sub Pattern**:
   - Standardize on "publish" for sending events and "subscribe" for receiving events
   - Rename signal methods to align with this terminology

2. **Module Interface Layer**:
   - Add a proper interface in the base module view for InfoBar interactions
   - Each module view should have methods like set_status(), clear_status(), etc.
   - Presenters should only interact with their view's interface, not the global service

3. **Event-Based Communication**:
   - Use the global event bus for cross-module communication
   - The main window should subscribe to relevant events and update the InfoBar

4. **Clear Ownership Boundaries**:
   - The main window owns the InfoBar widget
   - Modules own their status messages
   - The event bus mediates communication

## Implementation Plan

1. **Enhance the InfoBarService**:
   - Fully standardize on pub/sub terminology
   - Add support for different message types (info, warning, error)

2. **Update Module Views**:
   - Add consistent interface methods for status updates
   - Ensure these methods publish to the InfoBarService

3. **Refactor Presenters**:
   - Remove direct InfoBarService dependencies
   - Use the view's interface methods instead

4. **Update Main Window**:
   - Ensure it properly subscribes to InfoBar events
   - Handle visibility and message updates consistently

## Code Flow Diagram

```
┌─────────────────┐     ┌───────────────────┐     ┌───────────────────┐
│                 │     │                   │     │                   │
│   Presenter     │────▶│   Module View     │────▶│   InfoBarService  │
│                 │     │   (Interface)     │     │   (Singleton)     │
│                 │     │                   │     │                   │
└─────────────────┘     └───────────────────┘     └─────────┬─────────┘
                                                            │
                                                            │
                                                            ▼
┌─────────────────┐     ┌───────────────────┐     ┌───────────────────┐
│                 │     │                   │     │                   │
│  Main Window    │◀────│   Event Bus       │◀────│   InfoBar Widget  │
│                 │     │                   │     │                   │
│                 │     │                   │     │                   │
└─────────────────┘     └───────────────────┘     └───────────────────┘
```

This approach maintains clean architectural boundaries while ensuring the InfoBar works consistently across all modules.
