"""
Transaction utilities for the categorize module.

Simple utilities for converting transactions to DataFrames with proper column names.
"""

import pandas as pd
from typing import List
from fm.core.standards.fm_standard_columns import StandardColumns


def transactions_to_dataframe(transactions: List) -> pd.DataFrame:
    """
    Convert Transaction objects to DataFrame with proper column names.
    
    Uses StandardColumns for canonical column mapping and ensures required
    columns (category, tags, notes) exist for the categorize module.
    
    Args:
        transactions: List of Transaction objects from database
        
    Returns:
        DataFrame with proper column names ready for UI display
    """
    if not transactions:
        return pd.DataFrame()
    
    # Convert transactions to list of dictionaries
    data = []
    for transaction in transactions:
        # Create dict from transaction attributes
        trans_dict = {}
        
        # Map core fields using StandardColumns db_names
        if hasattr(transaction, 'date') and transaction.date:
            trans_dict[StandardColumns.DATE.db_name] = transaction.date
        if hasattr(transaction, 'description'):
            trans_dict[StandardColumns.DETAILS.db_name] = transaction.description
        if hasattr(transaction, 'amount'):
            trans_dict[StandardColumns.AMOUNT.db_name] = transaction.amount
        if hasattr(transaction, 'account_number'):
            trans_dict[StandardColumns.ACCOUNT.db_name] = transaction.account_number
        
        # Add other standard fields if they exist
        if hasattr(transaction, 'transaction_type'):
            trans_dict[StandardColumns.PAYMENT_TYPE.db_name] = transaction.transaction_type
        if hasattr(transaction, 'source_file'):
            trans_dict[StandardColumns.SOURCE_FILENAME.db_name] = transaction.source_file
        if hasattr(transaction, 'balance'):
            trans_dict[StandardColumns.BALANCE.db_name] = transaction.balance
        if hasattr(transaction, 'unique_id'):
            trans_dict[StandardColumns.UNIQUE_ID.db_name] = transaction.unique_id
            
        # Add categorize-specific fields
        if hasattr(transaction, 'category'):
            trans_dict['category'] = transaction.category or ""
        if hasattr(transaction, 'tags'):
            trans_dict['tags'] = transaction.tags or ""
        if hasattr(transaction, 'notes'):
            trans_dict['notes'] = transaction.notes or ""
            
        # Add system fields
        if hasattr(transaction, 'transaction_id'):
            trans_dict['id'] = transaction.transaction_id
        if hasattr(transaction, 'source_bank'):
            trans_dict['source_bank'] = transaction.source_bank
        if hasattr(transaction, 'import_date'):
            trans_dict['import_date'] = transaction.import_date
        if hasattr(transaction, 'modified_date'):
            trans_dict['modified_date'] = transaction.modified_date
            
        data.append(trans_dict)
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Ensure required columns exist for categorize module
    required_columns = ['category', 'tags', 'notes']
    for col in required_columns:
        if col not in df.columns:
            df[col] = ""
    
    return df
