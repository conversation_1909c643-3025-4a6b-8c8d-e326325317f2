"""
Transaction utilities for the categorize module.

Simple utilities for converting transactions to DataFrames with proper column names.
Uses the new ColumnNameService for elegant column handling.
"""

import pandas as pd
from typing import List
from fm.core.standards.column_name_service import ColumnNameService


def transactions_to_dataframe(transactions: List) -> pd.DataFrame:
    """
    Convert Transaction objects to DataFrame with proper db_name columns.

    This is now a simple wrapper around ColumnNameService.convert_transactions_to_dataframe
    which provides the elegant solution for transaction conversion.

    Args:
        transactions: List of Transaction objects from database

    Returns:
        DataFrame with db_name columns ready for TableView display
    """
    # Use the elegant ColumnNameService method
    return ColumnNameService.convert_transactions_to_dataframe(
        transactions,
        ensure_columns=['category', 'tags', 'notes']  # Categorize module requirements
    )
