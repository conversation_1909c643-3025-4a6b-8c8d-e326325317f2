import re
from dataclasses import dataclass

import pandas as pd
from fm.core.standards.fm_standard_columns import StandardColumns

from ._base_statement_handler import StatementHandler


@dataclass
class KiwibankBasicCSVHandler(StatementHandler):
    def __post_init__(self):
        self.statement_format = self.StatementFormat(
            bank_name="Kiwibank",
            variant="basic",
            file_type="csv",
        )

        # Column structure - only validate account number in header
        self.column_attrs = self.ColumnAttributes(
            n_source_cols=5,
            has_col_names=False,  # Don't validate standard headers
            has_account_column=False,
            source_col_names=[],  # No standard header names to check
            target_col_names=[
                StandardColumns.DATE,
                StandardColumns.DETAILS,
                StandardColumns.EMPTY_COLUMN,
                StandardColumns.AMOUNT,
                StandardColumns.BALANCE,
            ],
            date_format="%d %b %Y",  # Kiwibank basic CSV uses DD MMM YYYY format (e.g., '13 Jun 2024')
        )

        # Account number appears in both filename and first column header
        self.account_num_attrs = self.AccountNumberAttributes(
            pattern=r"38-?\d{4}-?\d{7}-?\d{2,3}",  # Just the account pattern
            in_file_name=True,  # Primary source
            in_header=True,  # Secondary validation in first column header
        )

        # No metadata rows in file
        self.source_metadata_attrs = self.SourceMetadataAttributes(
            has_metadata_rows=False
        )

    def _extract_account_number(self, df: pd.DataFrame) -> str:
        """Extract Kiwibank-specific account number from the first column header"""
        # Import logging here to avoid circular imports
        from fm.core.services.logger import Logger

        logger = Logger()
        handler_name = self.__class__.__name__

        # For Kiwibank CSVs, the account number is in the first column header
        if len(df.columns) > 0:
            first_column_name = str(df.columns[0])
            match = re.search(self.account_num_attrs.pattern, first_column_name)
            if match:
                account_number = match.group(0)
                logger.debug(
                    f"[{handler_name}] Found account number in first column header: {account_number}"
                )
                return account_number

        # If we couldn't find it for some reason, log a message
        logger.debug(
            f"[{handler_name}] Could not find account number in first column header"
        )
        return ""
