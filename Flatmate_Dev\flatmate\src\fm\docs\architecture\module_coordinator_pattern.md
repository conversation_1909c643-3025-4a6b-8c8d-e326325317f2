# Module Coordinator Pattern

## Overview

This document explains the Module Coordinator pattern used in the FlatMate application, including dependency injection via the `set_module_manager` method, the singleton question, and state persistence between modules.

## The Module Coordinator

The Module Coordinator is a central component responsible for:

1. Managing module lifecycle (creation, initialization, cleanup)
2. Handling transitions between modules
3. Maintaining module factories
4. Coordinating application state during module transitions

## Dependency Injection vs. Singleton

### Current Approach: Dependency Injection

The current implementation uses **dependency injection** rather than a singleton pattern:

```python
# In main.py
coordinator = ModuleCoordinator(main_window)
coordinator.initialize_modules()
main_window.set_module_manager(coordinator)
```

The `set_module_manager` method is a clean interface that allows the MainWindow to receive the ModuleCoordinator instance without creating it:

```python
# In main_window.py
def set_module_manager(self, module_manager):
    """Set the module manager instance."""
    self.module_manager = module_manager
```

### Why Dependency Injection?

1. **Cleaner Testing**: Components can be tested in isolation with mock dependencies
2. **Explicit Dependencies**: Dependencies are clearly visible in the code
3. **Flexible Configuration**: Different coordinator implementations can be injected
4. **Avoids Global State**: No hidden dependencies or global state issues

### Singleton Alternative

A singleton pattern would look like:

```python
class ModuleCoordinator:
    _instance = None
    
    @classmethod
    def get_instance(cls, main_window=None):
        if cls._instance is None:
            if main_window is None:
                raise ValueError("Main window required for first initialization")
            cls._instance = cls(main_window)
        return cls._instance
```

### Why Not Singleton?

1. **Hidden Dependencies**: Makes dependencies implicit and harder to track
2. **Testing Challenges**: Global state makes unit testing more difficult
3. **Thread Safety Concerns**: Requires additional complexity for thread safety
4. **Initialization Order Issues**: Can create subtle bugs with initialization order

## State Persistence Between Modules

### Current Implementation

The current implementation does not explicitly persist state between modules. When transitioning:

1. The current module is cleaned up via its `cleanup()` method
2. A new module is created from its factory
3. The new module is initialized with parameters

```python
def transition_to(self, module_name: str, **params: Dict[str, Any]):
    # Cleanup current module if it exists
    if self.current_module:
        if hasattr(self.current_module, 'cleanup'):
            self.current_module.cleanup()
        self.current_module = None
    
    # Create and initialize new module
    if module_name in self.module_factories:
        self.current_module = self.module_factories[module_name]()
        # Initialize the module
        if hasattr(self.current_module, 'initialize'):
            self.current_module.initialize(**params)
```

### State Persistence Options

Several approaches could be implemented for state persistence:

1. **Module-Specific State Storage**:
   - Each module saves its state before cleanup
   - State is retrieved during initialization
   - Example: `module.save_state()` before cleanup, then pass state in `initialize()`

2. **Coordinator-Managed State**:
   - ModuleCoordinator maintains a state dictionary for each module
   - State is automatically saved during transitions
   - Example: `self.module_states[module_name] = current_module.get_state()`

3. **External State Service**:
   - A dedicated StateService maintains application state
   - Modules interact with this service directly
   - Coordinator doesn't need to manage state explicitly

4. **Event-Based State Sharing**:
   - Modules publish state changes via events
   - Other modules subscribe to relevant events
   - State flows through the event system, not the coordinator

## Module Manager vs. Module Coordinator

The Module Manager was likely a separate component that has been consolidated into the ModuleCoordinator. This simplifies the architecture by:

1. **Reducing Components**: Fewer components means simpler mental model
2. **Clarifying Responsibilities**: One component handles all module-related tasks
3. **Eliminating Coordination Overhead**: No need to synchronize between manager and coordinator

## Best Practices

1. **Keep the Coordinator Focused**: It should only manage module lifecycle and transitions
2. **Use Clean Interfaces**: Modules should have consistent initialize/cleanup methods
3. **Prefer Explicit Dependencies**: Use dependency injection over singletons
4. **Consider State Management Needs**: Choose appropriate state persistence based on requirements
5. **Document Module Lifecycle**: Clearly define how modules are created, initialized, and cleaned up

## Conclusion

The dependency injection approach with `set_module_manager` provides a clean, testable architecture that avoids the complexity and hidden dependencies of singletons. For state persistence, consider implementing one of the suggested approaches based on the specific needs of your application.

The consolidation of module management into a single ModuleCoordinator simplifies the architecture and clarifies responsibilities, making the codebase more maintainable and easier to understand.
