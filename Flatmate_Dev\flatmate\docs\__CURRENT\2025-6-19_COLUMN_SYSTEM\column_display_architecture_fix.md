# Column Display Architecture Fix

## Problem Statement

The current table system has a fundamental architectural flaw: the column visibility menu only shows columns that are currently in the table model, preventing users from accessing hidden columns like 'category', 'notes', and 'tags'.

## Root Cause

The issue stems from a design where:
1. Only "default visible" columns are added to the table model
2. The visibility menu iterates over model columns only
3. Hidden columns are completely absent from the model

## Proposed Solution: Option 1 (Immediate Fix)

Based on the assessment, we'll implement **Option 1: Modify Table Model** as it provides the cleanest solution with minimal breaking changes.

### Implementation Plan

#### Step 1: Enhance ColumnManager
Add method to get all available columns for a module:

```python
def get_available_columns_for_module(self, module_name: str) -> List[str]:
    """Get all columns available for a module (not just visible ones)."""
    config_key = f"{module_name}.display.available_columns"
    return self.preferences.get_config_value(config_key, self._get_default_available_columns(module_name))
```

#### Step 2: Modify TransactionViewPanel
Update to add ALL available columns to the model:

```python
def set_transactions(self, df: pd.DataFrame):
    # 1. Get all available columns
    column_manager = get_column_manager()
    available_columns = config.get_value('categorize.display.available_columns', [])
    
    # 2. Ensure DataFrame has all available columns
    for col in available_columns:
        if col not in df.columns:
            df[col] = ""  # Add missing columns with empty values
    
    # 3. Reorder DataFrame to match available columns order
    df_ordered = df[available_columns]
    
    # 4. Set ALL columns in table model
    self.transaction_table.set_dataframe(df_ordered)
    column_mapping = column_manager.get_column_display_mapping("categorize")
    self.transaction_table.set_display_columns(available_columns, column_mapping)
    
    # 5. Apply default visibility (hide non-default columns)
    default_visible = config.get_value('categorize.display.default_visible_columns', [])
    for i, col in enumerate(available_columns):
        should_hide = col not in default_visible
        self.transaction_table.table_view.setColumnHidden(i, should_hide)
```

#### Step 3: Update Column Visibility Menu
The existing menu will now automatically show all columns since they're all in the model.

### Benefits

1. **Immediate Fix**: Solves the visibility menu problem
2. **Minimal Changes**: Uses existing infrastructure
3. **Backward Compatible**: Doesn't break existing functionality
4. **Future Proof**: Easy to add new columns

### Implementation

Let's implement this fix now.

## Implementation Details

### File Changes Required

1. **transaction_view_panel.py**: Modify `set_transactions()` method
2. **column_manager.py**: Add helper method (optional enhancement)
3. **Config**: Already updated with `available_columns` vs `default_visible_columns`

### Testing Strategy

1. Verify all columns appear in visibility menu
2. Confirm default visibility works correctly
3. Test column show/hide functionality
4. Validate data integrity in hidden columns

## Future Enhancements

### Phase 2: Enhanced Column Manager Integration
- Add `get_available_columns_for_module()` method
- Implement `prepare_full_dataframe_for_module()` helper
- Create `ColumnModuleInfo` data class for complete column information

### Phase 3: Dynamic Column Management
- Support for runtime column addition/removal
- User-customizable column sets
- Module-specific column templates

## Risk Assessment

### Low Risk
- Uses existing table model infrastructure
- Maintains current API contracts
- Backward compatible

### Mitigation
- Thorough testing of column visibility
- Validation of data integrity
- Performance testing with larger column sets

## Success Criteria

1. ✅ Column visibility menu shows ALL available columns
2. ✅ Default column visibility works as configured
3. ✅ Hidden columns retain data when shown
4. ✅ No performance degradation
5. ✅ Existing functionality unchanged
