# Column Visibility System Assessment

## ⚠️ CRITICAL ARCHITECTURAL ERROR IDENTIFIED

**Issue**: The implementation incorrectly used `EnhancedStandardColumns` (AI-created speculative system) instead of the canonical `StandardColumns` system as specified by the user.

**User Requirement**: "The canonical column names are in fm_standard_columns"

**What Happened**: AI chose to use the enhanced_columns system without explicit user approval, violating the established architecture.

**Correction Needed**: All column operations should use `StandardColumns` as the primary system, with database schema introspection for dynamic column discovery.

## Current Problem

The column visibility menu only shows columns currently in the table model, not all available columns. This prevents users from accessing hidden columns like 'category', 'notes', etc.

## Root Cause Analysis

### Current Flow:
1. **DataFrame Creation**: `cat_presenter.py` creates DataFrame with all columns including category, notes, tags
2. **Column Filtering**: `transaction_view_panel.py` filters to only `default_visible_columns` 
3. **Model Population**: Only filtered columns are added to the table model
4. **Visibility Menu**: Only shows columns in the model (limited set)

### The Issue:
```python
# In transaction_view_panel.py line 125:
valid_columns = [col for col in display_columns if col in df_copy.columns]
# This filters OUT columns that aren't in display_columns

# In fm_table_view.py line 98:
for col in range(self.table_view._model.columnCount()):
# This only iterates over columns IN the model (limited set)
```

## Available Helper Methods

### StandardColumns (Canonical - Primary System)
```python
# Basic mapping methods - THIS IS THE CANONICAL SYSTEM
StandardColumns.get_db_column_mapping()  # display_name -> db_name
StandardColumns.from_display_name(name)  # Find by display name
```

### EnhancedStandardColumns (Speculative - AI-Created System)
**⚠️ IMPORTANT NOTE**: This system was created by AI and is speculative. The user specifically stated that canonical column names are in `fm_standard_columns`, not this enhanced system.
```python
# Comprehensive mapping methods
EnhancedStandardColumns.get_db_to_display_mapping()     # db_name -> display_name
EnhancedStandardColumns.get_display_to_db_mapping()     # display_name -> db_name
EnhancedStandardColumns.get_field_to_db_mapping()       # field_name -> db_name
EnhancedStandardColumns.get_db_to_field_mapping()       # db_name -> field_name

# Search methods
EnhancedStandardColumns.find_by_db_name(name)           # Find by database name
EnhancedStandardColumns.find_by_display_name(name)      # Find by display name
EnhancedStandardColumns.find_by_field_name(name)        # Find by field name

# Column categorization
EnhancedStandardColumns.get_default_column_order()      # Ordered by sort_order
EnhancedStandardColumns.get_required_columns()          # Must be visible
EnhancedStandardColumns.get_editable_columns()          # User can edit
```

## Solution Options

### Option 1: Modify Table Model (Recommended)
**Approach**: Add ALL available columns to model, hide non-default ones

**Pros**:
- Clean separation of concerns
- Column visibility menu shows all columns
- Maintains existing API
- Uses existing helper methods

**Implementation**:
```python
# In transaction_view_panel.py:
def set_transactions(self, df: pd.DataFrame):
    # 1. Add ALL available columns to DataFrame
    available_columns = config.get_value('categorize.display.available_columns', [])
    for col in available_columns:
        if col not in df.columns:
            df[col] = ""  # Add missing columns with empty values
    
    # 2. Set ALL columns in table model
    self.transaction_table.set_dataframe(df)
    self.transaction_table.set_display_columns(available_columns, column_mapping)
    
    # 3. Hide non-default columns
    default_visible = config.get_value('categorize.display.default_visible_columns', [])
    for i, col in enumerate(available_columns):
        should_hide = col not in default_visible
        self.transaction_table.table_view.setColumnHidden(i, should_hide)
```

### Option 2: Enhanced Column Visibility Menu
**Approach**: Modify visibility menu to use config instead of model

**Pros**:
- Minimal changes to existing code
- Uses configuration system

**Cons**:
- Doesn't solve underlying architecture issue
- More complex menu logic

### Option 3: Column Manager Integration
**Approach**: Use ColumnManager to handle all column operations

**Pros**:
- Leverages existing enhanced column system
- Centralized column management
- Future-proof

**Implementation**:
```python
# Enhanced approach using ColumnManager
def _show_column_visibility(self):
    menu = QMenu(self)
    column_manager = get_column_manager()
    
    # Get ALL available columns for the module
    available_columns = column_manager.get_available_columns_for_module("categorize")
    display_mapping = column_manager.get_column_display_mapping("categorize")
    
    for db_name in available_columns:
        display_name = display_mapping.get(db_name, db_name)
        action = QAction(display_name, self)
        action.setCheckable(True)
        
        # Check if column is currently visible
        col_index = self._find_column_index(db_name)
        action.setChecked(col_index >= 0 and not self.table_view.isColumnHidden(col_index))
        
        # Connect to show/hide logic
        action.triggered.connect(lambda checked, col=db_name: self._toggle_column(col, checked))
        menu.addAction(action)
```

## Recommended Implementation

### Phase 1: Immediate Fix (Option 1)
1. **Modify `transaction_view_panel.py`**:
   - Add all available columns to DataFrame
   - Set all columns in table model
   - Hide non-default columns initially

2. **Update configuration**:
   - Use `available_columns` for all possible columns
   - Use `default_visible_columns` for initial visibility

### Phase 2: Enhanced Integration (Option 3)
1. **Add method to ColumnManager**:
   ```python
   def get_available_columns_for_module(self, module_name: str) -> List[str]:
       """Get all columns available for a module (not just visible ones)."""
   ```

2. **Update table visibility system**:
   - Use ColumnManager for all column operations
   - Implement proper show/hide logic for dynamic columns

## Developer-Friendly API Design

### Proposed ColumnManager Enhancement
```python
class ColumnManager:
    def get_full_column_info_for_module(self, module_name: str) -> ColumnModuleInfo:
        """Get complete column information for a module."""
        return ColumnModuleInfo(
            available_columns=self.get_available_columns_for_module(module_name),
            default_visible=self.get_default_visible_columns_for_module(module_name),
            display_mapping=self.get_column_display_mapping(module_name),
            width_mapping=self.get_column_widths(module_name),
            editable_columns=self.get_editable_columns(module_name)
        )
    
    def prepare_full_dataframe_for_module(self, df: pd.DataFrame, module_name: str) -> pd.DataFrame:
        """Ensure DataFrame has all available columns for a module."""
        available_columns = self.get_available_columns_for_module(module_name)
        for col in available_columns:
            if col not in df.columns:
                df[col] = ""  # Add missing columns
        return df[available_columns]  # Reorder to match available columns
```

### Usage Example
```python
# In transaction_view_panel.py:
def set_transactions(self, df: pd.DataFrame):
    column_manager = get_column_manager()
    
    # Get complete column info
    col_info = column_manager.get_full_column_info_for_module("categorize")
    
    # Prepare DataFrame with all available columns
    full_df = column_manager.prepare_full_dataframe_for_module(df, "categorize")
    
    # Set up table with all columns
    self.transaction_table.set_dataframe(full_df)
    self.transaction_table.set_display_columns(col_info.available_columns, col_info.display_mapping)
    
    # Apply default visibility
    for i, col in enumerate(col_info.available_columns):
        should_hide = col not in col_info.default_visible
        self.transaction_table.table_view.setColumnHidden(i, should_hide)
```

## Benefits of Recommended Approach

1. **Developer Friendly**: Simple, clear API using existing helper methods
2. **Maintainable**: Centralized column management through ColumnManager
3. **Flexible**: Easy to add new columns or change defaults
4. **Consistent**: Uses established patterns from enhanced column system
5. **Future Proof**: Supports dynamic column addition/removal

## Next Steps

1. Implement Phase 1 immediate fix
2. Test with existing categorize module
3. Enhance ColumnManager with proposed methods
4. Update documentation and examples
5. Apply pattern to other modules
