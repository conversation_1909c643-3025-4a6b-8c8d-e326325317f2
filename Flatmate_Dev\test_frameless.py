#!/usr/bin/env python3
"""
Test script for PyQt-Frameless-Window integration.
"""

import sys
import os

# Add the src directory to the path so we can import fm modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'flatmate', 'src'))

from PySide6.QtWidgets import QApplication
from fm.gui.main_window import MainWindow

def test_frameless_window():
    """Test the frameless window integration."""
    print("Testing PyQt-Frameless-Window integration...")
    
    app = QApplication(sys.argv)
    
    try:
        # Create main window
        window = MainWindow()
        print("✅ MainWindow created successfully")
        
        # Show window
        window.show()
        print("✅ Window displayed successfully")
        
        print("\n🎯 Test the maximize/restore functionality:")
        print("   - Click the maximize button (should work with single click)")
        print("   - Click the restore button (should work with single click)")
        print("   - Verify no debug output appears")
        
        # Run the application
        return app.exec()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_frameless_window())
