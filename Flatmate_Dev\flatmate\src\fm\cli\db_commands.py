"""Database command-line interface commands."""
import argparse
import csv
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# Set up basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

try:
    # Import from installed package
    from fm.database_service.repository.transaction_repository import Transaction
    from fm.database_service.repository.sqlite_repository import SQLiteTransactionRepository
    
    # Try to import StandardColumns, but make it optional
    try:
        from fm.core.standards.fm_standard_columns import StandardColumns
    except ImportError:
        logger.warning("Could not import StandardColumns, some features may be limited")
        StandardColumns = None
        
except ImportError as e:
    logger.error(f"Failed to import required modules: {e}")
    logger.error("Please ensure the package is properly installed in development mode")
    logger.error("Run: pip install -e . from the project root")
    sys.exit(1)

def get_transactions(args) -> List[Transaction]:
    """Retrieve transactions based on command line arguments."""
    repo = SQLiteTransactionRepository()
    
    filters = {}
    if hasattr(args, 'start_date') and args.start_date:
        filters["start_date"] = args.start_date
    if hasattr(args, 'end_date') and args.end_date:
        filters["end_date"] = args.end_date
    if hasattr(args, 'limit') and args.limit:
        filters["limit"] = args.limit
    
    return repo.get_transactions(**filters)

def list_transactions(args) -> None:
    """List transactions to the console."""
    transactions = get_transactions(args)
    if not transactions:
        print("No transactions found.")
        return
    
    # Print header
    headers = [
        "Date",
        "Description",
        "Amount",
        "Account",
        "Type",
        "Category"
    ]
    print("\n" + " | ".join(f"{h:<15}" for h in headers))
    print("-" * 100)
    
    # Print transactions
    for t in transactions:
        print(
            f"{t.date.strftime('%d-%m-%Y') if t.date else 'N/A':<15} | "
            f"{t.description[:30]:<15} | "
            f"{t.amount:<10.2f} | "
            f"{t.account_number:<15} | "
            f"{t.transaction_type:<10} | "
            f"{t.category}"
        )

def export_transactions(args) -> None:
    """Export transactions to CSV."""
    transactions = get_transactions(args)
    if not transactions:
        print("No transactions to export.")
        return
    
    # Prepare data
    data = []
    for t in transactions:
        data.append({
            "Date": t.date.strftime("%d-%m-%Y") if hasattr(t, 'date') and t.date else "",
            "Description": getattr(t, 'description', ''),
            "Amount": getattr(t, 'amount', ''),
            "Account": getattr(t, 'account_number', ''),
            "Type": getattr(t, 'transaction_type', ''),
            "Category": getattr(t, 'category', ''),
            "Source File": getattr(t, 'source_file', ''),
            "Import Date": t.import_date.strftime("%d-%m-%Y") if hasattr(t, 'import_date') and t.import_date else "",
        })
    
    # Generate default filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_filename = f"transactions_export_{timestamp}.csv"
    output_path = Path.cwd() / default_filename
    
    # Write to CSV
    try:
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            if not data:
                return
                
            # Get fieldnames from the first transaction
            fieldnames = list(data[0].keys())
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
            
        print(f"Exported {len(transactions)} transactions to {output_path}")
        print(f"Full path: {output_path.absolute()}")
        
    except Exception as e:
        print(f"Error exporting to CSV: {e}")

def show_stats(_) -> None:
    """Show database statistics."""
    repo = SQLiteTransactionRepository()
    
    # Get all transactions
    transactions = repo.get_transactions()
    
    if not transactions:
        print("No transactions found in the database.")
        return
    
    # Calculate basic statistics
    total_transactions = len(transactions)
    
    # Get date range
    dates = [t.date for t in transactions if hasattr(t, 'date') and t.date]
    earliest_date = min(dates) if dates else "N/A"
    latest_date = max(dates) if dates else "N/A"
    
    # Count by account
    accounts = {}
    for t in transactions:
        if hasattr(t, 'account_number') and t.account_number:
            accounts[t.account_number] = accounts.get(t.account_number, 0) + 1
    
    # Print statistics
    print("\n=== Database Statistics ===")
    print(f"Total transactions: {total_transactions}")
    print(f"Earliest date: {earliest_date}" if earliest_date != "N/A" else "No dates found")
    print(f"Latest date: {latest_date}" if latest_date != "N/A" else "No dates found")
    
    if accounts:
        print("\nTransactions by account:")
        for account, count in accounts.items():
            print(f"  - {account}: {count}")
    else:
        print("\nNo account information available.")

def delete_all_transactions(_) -> None:
    """Delete all transactions from the database."""
    confirm = input("Are you sure you want to delete ALL transactions? This cannot be undone. (y/n): ")
    if confirm.lower() == 'y':
        repo = SQLiteTransactionRepository()
        count = repo.delete_all_transactions()
        print(f"Deleted {count} transactions.")
    else:
        print("Operation cancelled.")

def create_parser() -> argparse.ArgumentParser:
    """Create and configure the argument parser."""
    parser = argparse.ArgumentParser(description="Flatmate Database Query Tool")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # List command
    list_parser = subparsers.add_parser("list", help="List transactions")
    list_parser.add_argument("--start-date", help="Start date (DD-MM-YY)")
    list_parser.add_argument("--end-date", help="End date (DD-MM-YY)")
    list_parser.add_argument("--limit", type=int, help="Limit number of transactions")
    list_parser.set_defaults(func=list_transactions)

    # Export command
    export_parser = subparsers.add_parser("export", help="Export transactions to CSV")
    export_parser.add_argument("--start-date", help="Start date (DD-MM-YY)")
    export_parser.add_argument("--end-date", help="End date (DD-MM-YY)")
    export_parser.set_defaults(func=export_transactions)

    # Stats command
    stats_parser = subparsers.add_parser("stats", help="Show database statistics")
    stats_parser.set_defaults(func=show_stats)

    # Delete all command
    delete_parser = subparsers.add_parser(
        "delete_all", help="Delete all transactions from the database"
    )
    delete_parser.set_defaults(func=delete_all_transactions)

    return parser

def main() -> None:
    """Main entry point for the CLI."""
    try:
        parser = create_parser()
        args = parser.parse_args()
        
        if hasattr(args, 'func'):
            args.func(args)
        else:
            parser.print_help()
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
