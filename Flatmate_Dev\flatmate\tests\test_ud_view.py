"""Test UpdateDataView implementation."""

import pytest
from PySide6.QtWidgets import QApplication
from fm.modules.update_data.ud_view import UpdateDataView

@pytest.fixture
def app():
    """Create QApplication instance."""
    return QApplication([])

def test_update_data_view_creation(app):
    """Test that UpdateDataView can be created."""
    view = UpdateDataView()
    assert view is not None
    assert hasattr(view, 'left_buttons')
    assert hasattr(view, 'display_area')
    assert hasattr(view, 'table_view')

def test_missing_setup_ui():
    """Test that missing setup_ui raises NotImplementedError."""
    class BadView(UpdateDataView):
        def setup_ui(self):
            pass  # Doesn't set up required widgets
    
    with pytest.raises(AttributeError):
        Bad<PERSON>iew()
