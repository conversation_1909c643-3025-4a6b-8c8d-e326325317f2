#!/usr/bin/env python3
"""
Tests for the data pipeline, focusing on data ordering and standardization.
"""

import os
import sys
import pandas as pd
import pytest
from pathlib import Path

# Add src directory to Python path
src_path = str(Path(__file__).parent.parent / "src")
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from flatmate.modules.update_data.utils.dw_pipeline import (
    load_csvs_to_collection,
    concat_reindex_drop_dupes,
)
from flatmate.modules.update_data.utils.dw_director import direct_pipeline

@pytest.fixture
def sample_csv_path(tmp_path):
    """Create a sample CSV file with test data."""
    csv_content = """account,transaction_date,description,amount
38-9004-0646977-00,01/12/2023,COFFEE,-5.00
38-9004-0646977-00,01/12/2023,LUNCH,-15.00
38-9004-0646977-00,02/12/2023,SALARY,1000.00"""
    
    file_path = tmp_path / "test.csv"
    with open(file_path, "w") as f:
        f.write(csv_content)
    return str(file_path)

@pytest.fixture
def sample_csv_path2(tmp_path):
    """Create a second sample CSV file with test data."""
    csv_content = """account,transaction_date,description,amount
38-9004-0646977-00,01/12/2023,DINNER,-25.00
38-9004-0646977-00,02/12/2023,GROCERIES,-50.00"""
    
    file_path = tmp_path / "test2.csv"
    with open(file_path, "w") as f:
        f.write(csv_content)
    return str(file_path)

def test_load_csv_preserves_order(sample_csv_path):
    """Test that loading a CSV preserves the original transaction order."""
    dfs, metadata_cols = load_csvs_to_collection([sample_csv_path])
    
    assert len(dfs) == 1
    df = dfs[0]
    
    # Check metadata columns
    assert "original_index" in metadata_cols
    assert "source_file" in metadata_cols
    
    # Check order preservation
    assert df.iloc[0]["description"] == "COFFEE"
    assert df.iloc[1]["description"] == "LUNCH"
    assert df.iloc[2]["description"] == "SALARY"
    
    # Check original_index
    assert list(df["original_index"]) == [0, 1, 2]

def test_concat_preserves_order_and_sorts_by_date(sample_csv_path, sample_csv_path2):
    """Test that concatenation preserves order within same dates and sorts by date."""
    # Debug: Print CSV contents
    print("\nCSV 1 contents:")
    with open(sample_csv_path, "r") as f:
        print(f.read())
    print("\nCSV 2 contents:")
    with open(sample_csv_path2, "r") as f:
        print(f.read())

    dfs, metadata_cols = load_csvs_to_collection([sample_csv_path, sample_csv_path2])
    
    # Debug: Print loaded DataFrames
    print("\nLoaded DataFrames:")
    for i, df in enumerate(dfs):
        print(f"\nDataFrame {i + 1}:")
        print(df[["transaction_date", "description", "source_file", "original_index"]].to_string())

    source_cols = ["account", "transaction_date", "description", "amount"]
    df_merged, _ = concat_reindex_drop_dupes(dfs, metadata_cols, source_cols)
    
    # Debug prints
    print("\nDebug info:")
    print("Merged DataFrame:")
    print(df_merged[["transaction_date", "description", "source_file", "original_index"]].to_string())
    
    # Convert date strings to datetime for comparison
    df_merged["transaction_date"] = pd.to_datetime(df_merged["transaction_date"], format="%d/%m/%Y")
    
    # Check sorting by date
    dates = df_merged["transaction_date"].tolist()
    sorted_dates = sorted(dates)
    print("\nDates vs Sorted Dates:")
    for d1, d2 in zip(dates, sorted_dates):
        print(f"{d1} {'==' if d1==d2 else '!='} {d2}")
    
    assert all(dates[i] <= dates[i + 1] for i in range(len(dates) - 1)), "Dates should be in ascending order"
    
    # Check order preservation within same date
    dec1_transactions = df_merged[df_merged["transaction_date"].dt.date == pd.to_datetime("2023-12-01").date()]
    print("\nDec 1st transactions:")
    print(dec1_transactions[["description", "source_file", "original_index"]].to_string())
    
    assert list(dec1_transactions["description"]) == ["COFFEE", "LUNCH", "DINNER"], \
        f"Wrong order for Dec 1st transactions: {list(dec1_transactions['description'])}"

def test_duplicate_handling(sample_csv_path):
    """Test that duplicates are handled correctly while preserving order."""
    # Create a duplicate entry
    with open(sample_csv_path, "a") as f:
        f.write("\n38-9004-0646977-00,01/12/2023,COFFEE,-5.00")
    
    dfs, metadata_cols = load_csvs_to_collection([sample_csv_path])
    source_cols = ["account", "transaction_date", "description", "amount"]
    df_merged, _ = concat_reindex_drop_dupes(dfs, metadata_cols, source_cols)
    
    # Should only have one COFFEE transaction
    coffee_transactions = df_merged[df_merged["description"] == "COFFEE"]
    assert len(coffee_transactions) == 1

def test_real_csv_pipeline():
    """Test the full pipeline using real CSV files."""
    # Setup test files
    csv1 = "/Users/<USER>/DEV/a_CODING_PROJECTS/a_flatmate_App_DEV/flatmate/tests/test_data/38-9004-0646977-00_01Oct.CSV"
    csv2 = "/Users/<USER>/DEV/a_CODING_PROJECTS/a_flatmate_App_DEV/flatmate/tests/test_data/38-9004-0646977-00_08Aug.CSV"
    
    # Create a job sheet
    job_sheet = {
        "job_id": "test_job",
        "output_dir": "/tmp/test_output",
        "bank_type": "kiwibank"
    }
    
    # Run the pipeline
    metadata_cols = ["source_file", "original_index", "fm_data"]
    result = direct_pipeline(job_sheet, [csv1, csv2], metadata_cols, update_master=True)
    
    # Check pipeline success
    assert result["status"] == "success", f"Pipeline failed: {result['message']}"
    assert len(result["details"]["processed_files"]) == 2, "Should process both files"
    assert len(result["details"]["unrecognized_files"]) == 0, "Should recognize all files"
    
    # Load and check the master CSV
    master_df = pd.read_csv(result["output_path"])
    
    # Verify date sorting
    master_df["transaction_date"] = pd.to_datetime(master_df["transaction_date"], format="%d %b %Y")
    dates = master_df["transaction_date"].tolist()
    assert all(dates[i] <= dates[i + 1] for i in range(len(dates) - 1)), "Dates should be in ascending order"
    
    # Verify source file tracking
    assert all(master_df["source_file"].notna()), "Source file should be tracked for all transactions"
    
    # Verify original indices
    assert all(master_df["original_index"].notna()), "Original index should be tracked for all transactions"
    
    # Verify amounts are preserved
    assert all(master_df["amount"].notna()), "Amount should be present for all transactions"
    
    # Verify no duplicate transactions (same date, description, and amount)
    dupes = master_df.duplicated(subset=["transaction_date", "description", "amount"], keep=False)
    assert not any(dupes), "Found duplicate transactions"

if __name__ == "__main__":
    pytest.main([__file__])
