"""Test BaseModuleView implementation."""

import pytest
from PySide6.QtWidgets import QApplication, QPushButton
from fm.modules.base.base_module_view import BaseModuleView

@pytest.fixture
def app():
    """Create QApplication instance."""
    return QApplication([])

def test_base_view(app):
    """Test that BaseModuleView works correctly."""
    class TestView(BaseModuleView):
        def setup_ui(self):
            self.button = QPushButton("Test")
        
        def setup_left_panel(self, layout):
            layout.addWidget(self.button)
    
    view = TestView()
    assert view is not None
    assert hasattr(view, 'button')

def test_base_view_missing_setup():
    """Test that missing setup_ui raises NotImplementedError."""
    class BadView(BaseModuleView):
        pass
    
    with pytest.raises(NotImplementedError):
        BadView()
