"""
Base person profile implementation for the FlatMate application.
Base class for manager and flatmate profiles.
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass
from .base_profile import BaseProfile, ContactInfo


@dataclass
class EmergencyContact:
    """Emergency contact information."""
    name: str = ""
    relationship: str = ""
    contact_info: ContactInfo = ContactInfo()


class PersonProfile(BaseProfile):
    """Base profile for any person in the system."""
    
    def __init__(self):
        super().__init__()
        self.id: str = ""  # Unique identifier
        self.name: str = ""
        self.contact_info: ContactInfo = ContactInfo()
        self.emergency_contact: EmergencyContact = EmergencyContact()
        self.notes: str = ""
        self.active: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert profile to dictionary format."""
        data = super().to_dict()
        data.update({
            "id": self.id,
            "name": self.name,
            "contact_info": {
                "email": self.contact_info.email,
                "phone": self.contact_info.phone,
                "address": self.contact_info.address
            },
            "emergency_contact": {
                "name": self.emergency_contact.name,
                "relationship": self.emergency_contact.relationship,
                "contact_info": {
                    "email": self.emergency_contact.contact_info.email,
                    "phone": self.emergency_contact.contact_info.phone,
                    "address": self.emergency_contact.contact_info.address
                }
            },
            "notes": self.notes,
            "active": self.active
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PersonProfile':
        """Create profile instance from dictionary data."""
        profile = cls()
        
        profile.id = data.get("id", "")
        profile.name = data.get("name", "")
        
        contact_data = data.get("contact_info", {})
        profile.contact_info = ContactInfo(
            email=contact_data.get("email", ""),
            phone=contact_data.get("phone", ""),
            address=contact_data.get("address", "")
        )
        
        emergency_data = data.get("emergency_contact", {})
        emergency_contact_info = emergency_data.get("contact_info", {})
        profile.emergency_contact = EmergencyContact(
            name=emergency_data.get("name", ""),
            relationship=emergency_data.get("relationship", ""),
            contact_info=ContactInfo(
                email=emergency_contact_info.get("email", ""),
                phone=emergency_contact_info.get("phone", ""),
                address=emergency_contact_info.get("address", "")
            )
        )
        
        profile.notes = data.get("notes", "")
        profile.active = data.get("active", True)
        
        return profile
