# Proposed Central Database Implementation

## Overview

This document outlines the proposed implementation of a central database for the Flatmate App. The database will serve as the primary storage for transaction data, replacing the current CSV-based master file approach while maintaining clean architectural boundaries between components.

## Design Principles

1. **Clean Separation of Concerns**
   - Data storage is separate from data processing
   - Export functionality is separate from data access
   - Each component has a clear, focused responsibility

2. **Proper Interfaces**
   - Repository pattern provides a clean interface to the database
   - No direct access to implementation details from business logic
   - Clear contracts between components

3. **Pub/Sub Communication**
   - Data changes publish events
   - UI components subscribe to relevant events
   - Maintains loose coupling between components

## Database Structure

### Technology Choice: SQLite

SQLite is recommended for the central database because it:
- Requires no server setup (file-based)
- Provides excellent performance for the expected data volume
- Supports complex queries and indexing
- Maintains portability (single file)
- Has robust Python support

### Location

The database will be stored at:
```
~/.flatmate/data/transactions.db
```

### Schema

The database schema will be based on the existing fm_standard columns, with additional fields for internal tracking:

```sql
CREATE TABLE transactions (
    id INTEGER PRIMARY KEY,
    date TEXT NOT NULL,
    description TEXT NOT NULL,
    amount REAL NOT NULL,
    account_number TEXT,
    transaction_type TEXT,
    category TEXT,
    notes TEXT,
    tags TEXT,
    source_bank TEXT,
    source_file TEXT,
    import_date TEXT,
    modified_date TEXT,
    is_deleted INTEGER DEFAULT 0,
    -- Additional fields as needed
    UNIQUE(date, description, amount, account_number)
);

-- Indexes for common queries
CREATE INDEX idx_transactions_date ON transactions(date);
CREATE INDEX idx_transactions_account ON transactions(account_number);
CREATE INDEX idx_transactions_category ON transactions(category);
```

## Implementation Architecture

The database implementation will follow a clean architecture with proper separation of concerns:

```
src/fm/data/
  ├── repository/           # Data access layer
  │   ├── transaction_repository.py  # Interface for transaction data
  │   └── sqlite_implementation.py   # SQLite implementation
  │
  ├── export/               # Export functionality
  │   ├── export_service.py          # Main export interface
  │   ├── filters.py                 # Filter definitions
  │   └── formatters/                # Output formatters
  │       ├── csv_formatter.py
  │       ├── excel_formatter.py
  │       └── pdf_formatter.py
  │
  └── models/               # Data models
      └── transaction.py              # Transaction model
```

### Key Interfaces

#### TransactionRepository

```python
class TransactionRepository:
    """Interface for transaction data storage."""
    
    def add_transactions(self, transactions: List[Transaction]) -> ImportResult:
        """Add new transactions to the repository."""
        pass
        
    def get_transactions(self, filters: Optional[Dict] = None) -> List[Transaction]:
        """Retrieve transactions matching the filters."""
        pass
        
    def update_transaction(self, transaction_id: int, data: Dict) -> bool:
        """Update a specific transaction."""
        pass
        
    def delete_transaction(self, transaction_id: int) -> bool:
        """Mark a transaction as deleted."""
        pass
        
    def get_statistics(self) -> Dict:
        """Get statistics about the stored transactions."""
        pass
```

#### ExportService

```python
class ExportService:
    """Service for exporting transaction data."""
    
    def export(self, 
               filters: Optional[Dict] = None, 
               format: str = "csv", 
               destination: str = None) -> ExportResult:
        """
        Export transactions based on filters in the specified format.
        
        Args:
            filters: Criteria for selecting transactions
            format: Output format (csv, excel, pdf)
            destination: Output file path
            
        Returns:
            ExportResult with status and file path
        """
        pass
```

## Integration with Existing Modules

### Data Import Module (currently Update Data)

The Data Import module will:
1. Continue to handle CSV import and standardization
2. Use the TransactionRepository to store imported data
3. Publish events when new data is imported

```python
# Example integration in the presenter
def process_files(self, files):
    # Process and standardize CSV files (existing logic)
    standardized_data = self.processor.process_files(files)
    
    # Store in the central database
    result = self.transaction_repository.add_transactions(standardized_data)
    
    # Publish event about new data
    self.event_bus.publish("transactions.imported", {
        "count": result.added_count,
        "duplicates": result.duplicate_count,
        "source_files": files
    })
    
    return result
```

### Accounts Module

The Accounts module will:
1. Use the TransactionRepository to retrieve and update data
2. Subscribe to events about data changes
3. Provide UI for viewing, filtering, and categorizing transactions

```python
# Example integration in the presenter
def initialize(self):
    # Subscribe to relevant events
    self.event_bus.subscribe("transactions.imported", self.refresh_data)
    self.event_bus.subscribe("transactions.updated", self.refresh_data)
    
    # Initial data load
    self.refresh_data()
    
def refresh_data(self, event_data=None):
    # Get current filters from view
    filters = self.view.get_current_filters()
    
    # Retrieve filtered data
    transactions = self.transaction_repository.get_transactions(filters)
    
    # Update view
    self.view.set_transactions(transactions)
```

### Profiles Module

The Profiles module will:
1. Store user preferences about data organization
2. Configure export templates
3. Define categorization rules

## Export Functionality

The export system will allow users to:
1. Filter transactions by any field (date, account, category, etc.)
2. Select output format (CSV, Excel, PDF)
3. Choose destination
4. Save export templates for repeated use

## Migration Plan

1. **Phase 1: Create Database Infrastructure**
   - Implement repository interfaces
   - Create SQLite implementation
   - Add basic import/export functionality

2. **Phase 2: Update Data Import Module**
   - Modify to use the central database
   - Maintain backward compatibility with CSV masters
   - Add import dashboard

3. **Phase 3: Enhance Accounts Module**
   - Connect to the central database
   - Implement filtering and categorization
   - Add reporting features

4. **Phase 4: Implement Export System**
   - Create flexible export filters
   - Implement multiple output formats
   - Add export templates

## Benefits

This central database implementation will:
1. Improve performance for large datasets
2. Enable more complex queries and filtering
3. Maintain data integrity
4. Provide a foundation for future features
5. Maintain clean architectural boundaries between components

The implementation follows the pub/sub pattern for communication between modules, ensuring loose coupling and maintainability.
