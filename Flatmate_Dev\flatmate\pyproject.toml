[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "flatmate"
version = "0.1.0"
authors = [
  { name="Flatmate Team" },
]
description = "Flatmate application"
requires-python = ">=3.11"
dependencies = [
    "PySide6",
    "pandas",
    "pyyaml",
]

[tool.setuptools]
packages = ["fm"]
package-dir = {"" = "src"}

[project.scripts]
flatmate = "fm.main:main"
dbq = "fm.cli.db_commands:main"
dbquery = "fm.cli.db_commands:main"
