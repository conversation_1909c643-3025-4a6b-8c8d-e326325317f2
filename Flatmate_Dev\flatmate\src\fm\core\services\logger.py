#!/usr/bin/env python3
"""
FlatMate Centralized Logger
==========================

This module provides event-based logging for the FlatMate application, supporting console and file output with configurable log levels.

Usage:
    from fm.core.services.logger import log, Logger, log_this
    log('A message', level='info')
    Logger.error('Something went wrong')
    
    @log_this('debug')
    def foo():
        ...

Configuration:
    - Log level and directory are read from the global config manager.
    - Console log threshold can be set in preferences.yaml (e.g., console_level: "DEBUG").
    - Directories are created at startup via AppPaths.ensure_directories().

See logger_readme.md for full details.
"""

# ===========================
# Imports
# ===========================
import os
import sys
import logging
from pathlib import Path
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional, Union, Callable, TypeVar
from functools import wraps
import inspect
from ..services.event_bus import global_event_bus, Events
from fm.core.config import config as global_config_manager
from fm.core.config.keys import ConfigKeys

# ===========================
# Public API
# ===========================
__all__ = [
    "log",
    "Logger", 
    "log_this",
    "LogLevel"
]

# ===========================
# Constants & Configuration
# ===========================
class LogLevel(Enum):
    """Standardized log levels"""
    DEBUG = 'DEBUG'
    INFO = 'INFO'
    WARNING = 'WARNING'
    ERROR = 'ERROR'
    CRITICAL = 'CRITICAL'

# Shorthand log level mapping
_SHORTHAND_LOG_LEVEL_MAP: Dict[str, LogLevel] = {
    'warn': LogLevel.WARNING, 'w': LogLevel.WARNING,
    'debug': LogLevel.DEBUG, 'd': LogLevel.DEBUG,
    'info': LogLevel.INFO, 'i': LogLevel.INFO,
    'error': LogLevel.ERROR, 'e': LogLevel.ERROR,
    'critical': LogLevel.CRITICAL, 'c': LogLevel.CRITICAL
}

# Global state
_CURRENT_LOG_FILE: Optional[str] = None
T = TypeVar('T', bound=Callable[..., Any])

# ===========================
# Core Logging Functions
# ===========================
def log(message: str, level: Optional[Union[LogLevel, str]] = None, module: Optional[str] = None) -> None:
    """
    Main logging function with shorthand level support.
    
    Args:
        message: Log message
        level: Logging level (shorthand or full LogLevel)
        module: Optional module name (defaults to caller's module)
    """
    # Determine log level
    if level is None:
        log_level = LogLevel.INFO
    elif isinstance(level, LogLevel):
        log_level = level
    elif isinstance(level, str):
        log_level = _SHORTHAND_LOG_LEVEL_MAP.get(level.lower(), LogLevel.INFO)
    else:
        log_level = LogLevel.INFO
    
    # Determine module if not provided
    module_to_log = module if module else _determine_log_source()
    
    # Publish to event bus (for UI notifications, etc.)
    global_event_bus.publish(Events.LOG_EVENT, {
        'level': log_level.value,
        'module': module_to_log,
        'message': message
    })

def setup_logging() -> 'Logger':
    """
    Set up unified logging system using event bus integration.
    
    Returns:
        Logger instance
    """
    global _CURRENT_LOG_FILE
    
    # 1. Get log directory from config
    try:
        log_dir_path_str = global_config_manager.get_path(ConfigKeys.Paths.LOGS)
        log_dir_path = Path(log_dir_path_str)
        log_dir_path.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        sys.stderr.write(f"[CRITICAL] Logger setup failed: {e}\n")
        raise

    # 2. Create log file for this session
    log_filename = f"flatmate-{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log"
    _CURRENT_LOG_FILE = str(log_dir_path / log_filename)

    return Logger()

def log_handler(log_event: Dict[str, Any]) -> None:
    """
    Event bus log handler: direct console and file output with proper module detection.
    
    Args:
        log_event: Dictionary containing log event details
    """
    level_str = log_event.get('level', LogLevel.INFO.value)
    module = log_event.get('module', 'UNKNOWN')
    message = log_event.get('message', '')
    
    # Console output with proper module name
    console_line = f"[{level_str}] [{module}] {message}"
    print(console_line)
    
    # File output if available
    global _CURRENT_LOG_FILE
    if _CURRENT_LOG_FILE:
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]
            file_line = f"{timestamp} - {level_str} - {module} - {message}\n"
            with open(_CURRENT_LOG_FILE, 'a', encoding='utf-8') as f:
                f.write(file_line)
        except Exception as e:
            print(f"Error writing to log file: {e}")

# Subscribe event handler to event bus
global_event_bus.subscribe(Events.LOG_EVENT, log_handler)

# ===========================
# Logger Class
# ===========================
class Logger:
    """Static logger class providing convenient logging methods."""
    
    @staticmethod
    def info(message: str, module: Optional[str] = None) -> None:
        log(message, level='info', module=module)
    
    @staticmethod
    def error(message: str, module: Optional[str] = None) -> None:
        log(message, level='error', module=module)
    
    @staticmethod
    def debug(message: str, module: Optional[str] = None) -> None:
        log(message, level='debug', module=module)
        
    @staticmethod
    def warning(message: str, module: Optional[str] = None) -> None:
        log(message, level='warning', module=module)
        
    @staticmethod
    def critical(message: str, module: Optional[str] = None) -> None:
        log(message, level='critical', module=module)

# ===========================
# Decorator
# ===========================
def log_this(level: Optional[Union[LogLevel, str]] = None) -> Callable[[T], T]:
    """
    Decorator for function entry/exit logging.
    
    Args:
        level: Logging level (can be LogLevel, string, or None)
    
    Returns:
        Decorated function with logging
    """
    def decorator(func: T) -> T:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            effective_level = level
            if isinstance(level, str):
                effective_level = _SHORTHAND_LOG_LEVEL_MAP.get(level.lower(), LogLevel.INFO)
            
            log(f'Entering {func.__name__}', effective_level)
            
            try:
                result = func(*args, **kwargs)
                log(f'Exiting {func.__name__}', effective_level)
                return result
            except Exception as e:
                log(f'Exception in {func.__name__}: {str(e)}', 'error')
                raise
        
        return wrapper  # type: ignore
    
    return decorator

# ===========================
# Internal Helper Functions
# ===========================
def _determine_log_source() -> str:
    """
    Determines the module name of the code that called the logger.
    Skips all logger-related frames to find the actual caller.
    
    Returns:
        Module name or fallback value
    """
    try:
        frame = inspect.currentframe()
        if not frame:
            return 'UNKNOWN_MODULE'
            
        # Skip through all frames that belong to the logger module
        current_frame = frame.f_back
        while current_frame:
            frame_module = current_frame.f_globals.get('__name__')
            
            # If we're still in the logger module, keep going
            if frame_module == __name__:
                current_frame = current_frame.f_back
                continue
            
            # Found a frame outside the logger module
            if frame_module == '__main__':
                filepath = current_frame.f_globals.get('__file__')
                if filepath:
                    return os.path.splitext(os.path.basename(filepath))[0]
                return '__main__(no_file)'
            elif frame_module:
                return frame_module
            else:
                return 'FRAME_HAS_NO_MODULE_NAME'
        
        return 'NO_CALLER_FRAME_FOUND'
            
    except Exception as e:
        return f'INSPECT_ERROR({type(e).__name__})'
    finally:
        # Clean up frame references
        if 'frame' in locals():
            del frame
        if 'current_frame' in locals():
            del current_frame
