Config Type,Module,Function,Parameter,Type,Default,Required,Description,Source File,Category,Runtime Order,Column1,Column2,Column3,Column4
,,,,,,,,,,,,,,
# CONFIG TYPE DEFINITIONS,,,,,,,,,,,,,,
# runtime_config: Can be changed while app is running (e.g., UI preferences),,,,,,,,,,,,,
# init_config: Set at startup, cannot change during runtime (e.g., paths),,,,,,,,,,,,
# const: Not configurable, hardcoded values (e.g., date formats),,,,,,,,,,,,
# env: Environment-specific settings (e.g., dev mode),,,,,,,,,,,,,
,,,,,,,,,,,,,,
# Runtime Configurations (Can change during execution),,,,,,,,,,,,,,
runtime_config,main,init,ui.theme_profile,str,None,no,UI theme stylesheet path,main.py,ui_settings,3,,,,
runtime_config,main,init,ui.font_size,int,14,no,Default application font size,main.py,ui_settings,3,,,,
runtime_config,main,init,window.size,tuple,"(1200, 800)",no,Initial window dimensions,main.py,ui_settings,4,,,,
runtime_config,window,init,ui.left_panel_width,int,250,no,Initial width of left panel,gui/main_window.py,ui_settings,4,,,,
runtime_config,window,init,ui.right_panel_width,int,250,no,Initial width of right panel,gui/main_window.py,ui_settings,4,,,,
runtime_config,update_data,init,logging.show_info,bool,TRUE,no,Show informational messages,utils/dw_pipeline.py,logging,1,,,,
runtime_config,update_data,init,logging.show_warnings,bool,TRUE,no,Show warning messages,utils/dw_pipeline.py,logging,1,,,,
,,,,,,,,,,,,,,
# Initialization Configurations (Set at startup),,,,,,,,,,,,,,
init_config,app,paths,LOGS_DIR,Path,~/logs,yes,Directory for application logs,core/config/paths.py,os,1,,,,
init_config,app,paths,DATA_DIR,Path,~/data,yes,Directory for application data,core/config/paths.py,os,1,,,,
init_config,app,paths,CONFIG_DIR,Path,~/config,yes,Directory for configuration files,core/config/paths.py,os,1,,,,
init_config,update_data,init,paths.backup_dir,Path,~/flatmate/backups,yes,Directory for original file backups,config/ud_config.py,os,1,,,,
init_config,update_data,init,paths.master_dir,Path,~/flatmate/master,yes,Directory for master CSV files,config/ud_config.py,os,1,,,,
init_config,update_data,init,paths.temp_dir,Path,~/flatmate/temp,yes,Directory for temporary files,config/ud_config.py,os,1,,,,
,,,,,,,,,,,,,,
# Constants (Not configurable),,,,,,,,,,,,,,
const,update_data,init,formats.kiwibank.simple.columns,list,[Account Number,Date,Description,Amount,Balance],yes,Required columns for simple format,utils/formatting/formats/kiwibank.py,constants,1
const,update_data,init,formats.kiwibank.full.columns,list,[Account number,Date,Memo/Description,Amount,Balance],yes,Required columns for full format,utils/formatting/formats/kiwibank.py,constants,1
const,update_data,init,formats.kiwibank.account_pattern,str,^38-\d{4}-\d{7}-\d{2}$,yes,Account number pattern,utils/formatting/formats/kiwibank.py,constants,1,,,,
const,update_data,init,formats.date_format,str,%d/%m/%Y,yes,Date format for files,utils/formatting/formats/kiwibank.py,constants,1,,,,
const,update_data,init,system.batch_size,int,1000,yes,Processing batch size,utils/dw_pipeline.py,constants,2,,,,
const,update_data,init,system.memory_limit,int,**********,yes,Memory limit (1GB),utils/dw_pipeline.py,constants,2,,,,
const,update_data,init,system.encoding,str,utf-8,yes,File encoding,utils/formatting/formats/bank_format.py,constants,2,,,,
,,,,,,,,,,,,,,
# Environment Settings,,,,,,,,,,,,,,
env,main,init,development_mode,bool,FALSE,yes,Development mode flag,main.py,environment,1,,,,
env,main,init,logging.level,str,INFO,no,Logging level for application,main.py,environment,2,,,,
env,update_data,init,env.development_mode,bool,FALSE,yes,Development mode for module,config/ud_config.py,environment,1,,,,