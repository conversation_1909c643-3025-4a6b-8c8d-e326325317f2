#!/usr/bin/env python3
"""
Test script for the home module.
"""

import sys
import os
import pytest
from unittest.mock import MagicMock, patch

# Add the project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# Mock modules before importing HomePresenter
sys.modules['src.flatmate.modules.utils.dw_director'] = MagicMock()
sys.modules['src.flatmate.modules.utils'] = MagicMock()

from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout
from PySide6.QtCore import Qt
from src.flatmate.modules.home.home_view import HomeView
from src.flatmate.modules.home.home_presenter import HomePresenter
from src.flatmate.modules.home.home_state import HomeState
from src.flatmate.modules.update_data import ud_view, ud_presenter

@pytest.fixture
def app():
    """Fixture for QApplication instance."""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    return app

@pytest.fixture
def home_view(app):
    """Fixture for HomeView instance."""
    view = HomeView()
    return view

@pytest.fixture
def mock_update_data_view(monkeypatch):
    """Mock UpdateDataView to avoid dependencies."""
    class MockUpdateDataView(QWidget):
        last_instance = None  # Class variable to track instances
        
        def __init__(self, parent=None):
            super().__init__(parent)
            self.setObjectName('MockUpdateDataView')
            self.layout = QVBoxLayout(self)
            # Store the instance for testing
            MockUpdateDataView.last_instance = self
            print("\n=== Created MockUpdateDataView instance ===")
    
    # Replace the actual UpdateDataView class
    ud_view.UpdateDataView = MockUpdateDataView
    
    # Patch both absolute and relative import paths
    monkeypatch.setattr('src.flatmate.modules.update_data.ud_view.UpdateDataView', MockUpdateDataView)
    monkeypatch.setattr('src.flatmate.modules.home.home_presenter.UpdateDataView', MockUpdateDataView)
    
    return MockUpdateDataView

@pytest.fixture
def mock_update_data_presenter(monkeypatch):
    """Mock UpdateDataPresenter to avoid dependencies."""
    class MockUpdateDataPresenter:
        def __init__(self, view):
            print("\n=== Initializing Update Data Presenter ===")
            print("Update Data Presenter initialization complete")
    
    # Replace the actual UpdateDataPresenter class
    ud_presenter.UpdateDataPresenter = MockUpdateDataPresenter
    
    # Patch both absolute and relative import paths
    monkeypatch.setattr('src.flatmate.modules.update_data.ud_presenter.UpdateDataPresenter', MockUpdateDataPresenter)
    monkeypatch.setattr('src.flatmate.modules.home.home_presenter.UpdateDataPresenter', MockUpdateDataPresenter)
    
    return MockUpdateDataPresenter

@pytest.fixture
def home_presenter(home_view, mock_update_data_view, mock_update_data_presenter):
    """Fixture for HomePresenter instance with mocked dependencies."""
    presenter = HomePresenter(home_view)
    return presenter

def test_initial_state(home_view, home_presenter):
    """Test initial state of the home module."""
    assert home_view is not None
    assert home_presenter is not None
    assert home_presenter.state is not None
    assert isinstance(home_presenter.state, HomeState)

def test_navigation_buttons_exist(home_view):
    """Test that all navigation buttons are present."""
    assert home_view.update_profile_btn is not None
    assert home_view.update_data_btn is not None
    assert home_view.view_accounts_btn is not None
    assert home_view.quit_btn is not None

def test_update_data_navigation(home_view, home_presenter, mock_update_data_view):
    """Test navigation to update data module."""
    # Reset the last instance
    mock_update_data_view.last_instance = None
    
    # Click the update data button
    home_view.update_data_btn.click()
    
    # Get the last created instance
    mock_view = mock_update_data_view.last_instance
    assert mock_view is not None, "MockUpdateDataView was not created"
    
    # Check if the mock view is in the content layout
    found = False
    for i in range(home_view.content_layout.count()):
        widget = home_view.content_layout.itemAt(i).widget()
        if widget and widget == mock_view:
            found = True
            break
    
    assert found, "MockUpdateDataView not found in content layout"
    assert isinstance(mock_view.parent(), QWidget), "MockUpdateDataView parent is not a QWidget"

def test_first_run_shows_splash(home_view, home_presenter, monkeypatch):
    """Test that splash screen shows on first run."""
    # Create a mock splash screen widget
    splash_widget = QWidget()
    splash_widget.setObjectName("splash_screen")
    
    # Patch the show_splash_screen method to use our mock widget
    def mock_show_splash():
        home_view._clear_content()
        home_view.content_layout.addWidget(splash_widget)
    
    monkeypatch.setattr(home_view, 'show_splash_screen', mock_show_splash)
    
    # Set first run state and initialize
    mock_state = MagicMock()
    mock_state.is_first_run = True
    monkeypatch.setattr(home_presenter, 'state', mock_state)
    home_presenter.initialize()
    
    # Check if splash screen is visible
    found_splash = False
    for i in range(home_view.content_layout.count()):
        widget = home_view.content_layout.itemAt(i).widget()
        if widget and widget.objectName() == "splash_screen":
            found_splash = True
            break
    
    assert found_splash, "Splash screen not found in content layout"

def test_quit_button(home_view, home_presenter, monkeypatch):
    """Test quit button functionality."""
    # Mock QApplication.quit
    mock_quit = MagicMock()
    monkeypatch.setattr(QApplication, 'quit', mock_quit)
    
    # Click quit button
    home_view.quit_btn.click()
    
    # Verify quit was called
    mock_quit.assert_called_once()

if __name__ == "__main__":
    pytest.main([__file__])
