"""
Test Home Presenter Module.
Handles the logic for the test home module and provides a main function for testing.
"""

from PySide6.QtWidgets import QApplication
from ..gui.main_window import MainWindow
from .test_home_view import TestHomeView

class TestHomePresenter:
    """Presenter for the test home module."""
    
    def __init__(self):
        self.view = TestHomeView()
        self.view.presenter = self
        
        # Connect signals
        self.view.button_clicked.connect(self.handle_button_click)
        self.view.text_changed.connect(self.handle_text_change)
    
    def handle_button_click(self, button_text):
        """Handle button clicks from the view."""
        if button_text == "Toggle Right Panel":
            if self.view.main_window:
                self.view.main_window.toggle_right_panel()
        
        elif button_text == "Update Center Text":
            self.view.update_status("Center text updated!")
        
        elif button_text == "Clear Right Panel":
            if self.view.main_window:
                self.view.main_window.clear_right_panel()
        
        elif button_text == "Test Action 1":
            self.view.update_status("Test Action 1 performed!")
        
        elif button_text == "Test Action 2":
            self.view.update_status("Test Action 2 performed!")
    
    def handle_text_change(self, text):
        """Handle text changes from the input field."""
        self.view.update_status(f"Text changed to: {text}")


def main():
    """Main function for testing the module independently."""
    import sys
    
    # Create the application
    app = QApplication(sys.argv)
    
    # Create and show the main window
    main_window = MainWindow()
    main_window.show()
    
    # Create the presenter (which creates the view)
    presenter = TestHomePresenter()
    
    # Set up the view in the main window
    presenter.view.setup_in_main_window(main_window)
    
    # Start the event loop
    sys.exit(app.exec())


if __name__ == '__main__':
    main()
