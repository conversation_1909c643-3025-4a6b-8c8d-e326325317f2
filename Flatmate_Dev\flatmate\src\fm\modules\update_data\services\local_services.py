"""Local services for Update Data module."""

from ....core.services import master_file_service as core_master_service
from ....core.services.logger import LogLevel, log, log_this


class UpdateDataServices:
    """Service wrapper for Update Data module.

    Provides module-specific access to core services with any
    additional module-specific functionality.
    """

    def __init__(self):
        self._master_service = core_master_service
        log("UpdateDataServices initialized", "d")

    @log_this(LogLevel.INFO)
    def update_master_location(self, file_path):
        """Update master file location with module-specific validation."""
        if self._master_service.validate_master(file_path):
            self._master_service.update_location(file_path)
            return True
        return False

    @log_this(LogLevel.DEBUG)
    def get_current_master(self):
        """Get current master file location."""
        return self._master_service.get_current_master()

    @log_this(LogLevel.DEBUG)
    def get_master_history(self):
        """Get master file history."""
        return self._master_service.get_history()


# Global instance
ud_services = UpdateDataServices()
