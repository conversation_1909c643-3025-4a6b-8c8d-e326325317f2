"""Test configuration for pytest."""

import os
import sys
from pathlib import Path

# Get the project root directory
project_root = Path(__file__).parent.parent

# Add src directory to PYTHONPATH
src_dir = str(project_root / 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Also add project root for imports like 'fm.modules...'
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))
