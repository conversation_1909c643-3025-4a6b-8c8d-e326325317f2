Category,Config Type,Module,Parameter,Type,Default,Required,Description,Source File

# CURRENT ESSENTIAL CONFIGS

# Environment
env,init,app,env.mode,str,development,yes,Environment mode (dev/prod),config/env.py
env,init,app,env.debug,bool,false,no,Show debug information,config/env.py

# Core Paths
sys,init,app,paths.project_root,Path,.,yes,Project root directory,core/config/paths.py
sys,init,app,paths.logs,Path,PROJECT_ROOT/logs,yes,Log files directory,core/config/paths.py
sys,init,app,paths.config,Path,PROJECT_ROOT/configs,yes,Config files directory,core/config/paths.py
sys,init,app,paths.resources,Path,PROJECT_ROOT/resources,yes,Resources directory,core/config/paths.py

# Logging
log,runtime,app,log.show_info,bool,true,no,Show info messages,core/logger.py
log,runtime,app,log.show_warnings,bool,true,no,Show warning messages,core/logger.py

# Update Data Module
sys,init,update_data,paths.master,Path,~/flatmate/master,yes,Master CSV location,modules/update_data/config/ud_config.py
sys,init,update_data,paths.backup,Path,~/flatmate/backup,yes,Backup directory,modules/update_data/config/ud_config.py
const,init,update_data,formats.date_format,str,%d/%m/%Y,yes,Bank date format,modules/update_data/utils/formatting/formats/bank_format.py

# Window Settings (These need to persist)
ui,runtime,app,window.left_panel_width,int,250,no,Left panel width,gui/main_window.py
ui,runtime,app,window.right_panel_width,int,250,no,Right panel width,gui/main_window.py
ui,runtime,app,window.column_widths,dict,{},no,Table column widths by table ID,gui/main_window.py

# Theme Constants (Hard to change in QSS - keep as constants)
const,init,app,theme.colors.primary,str,#3B8A45,yes,Primary theme color,gui/styles/constants.py
const,init,app,theme.colors.background,str,#2C2C2C,yes,Background color,gui/styles/constants.py
const,init,app,theme.font.size,int,12,yes,Base font size,gui/styles/constants.py
