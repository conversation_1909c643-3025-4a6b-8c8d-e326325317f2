# Central Database Implementation

## Overview

The central database system in Flatmate provides a unified storage solution for financial transactions. It allows various modules to store, retrieve, and manipulate transaction data while maintaining consistent data formats and relationships.

> **Note**: For practical code examples and quick reference, see the [Data System README](../../../src/fm/data/README.md) in the source directory.

## Architecture

The database implementation follows clean architectural boundaries with proper interfaces between components:

1. **Data Models** - Define the structure of data entities
2. **Repositories** - Handle data access operations
3. **Services** - Provide business logic and coordinate operations
4. **Converters** - Transform data between different formats

## Key Components

### Transaction Model

The `Transaction` class in `fm.data.repository.transaction_repository` defines the core data structure for financial transactions. It includes:

- Essential transaction details (date, description, amount)
- Account information
- Categorization data
- Source tracking
- Import metadata

### Data Service

The `data_service` singleton in `fm.data` provides a unified interface for database operations:

- Import transactions
- Query transactions by various criteria
- Update transaction details
- Generate statistics

### CSV Converter

The `CSVToTransactionConverter` in `fm.data.converters` transforms CSV data into Transaction objects:

- Maps CSV fields to transaction attributes
- Handles different date and number formats
- Uses standardized column names from `FmColumnFormat`
- Provides fallbacks for non-standard column names

## Integration with Update Data Module

The Update Data module can save processed transactions directly to the central database:

1. User selects bank statement files
2. Files are processed through the data pipeline
3. If "Update Database" is checked, transactions are saved to both:
   - A master CSV file (with dates in UK format: DD-MM-YY)
   - The central database (with dates in ISO format: YYYY-MM-DD)

### Implementation Details

- The `dw_pipeline.py` handles both CSV saving and database updates
- Date formats are standardized appropriately for each destination
- The database update process uses the same converter as other parts of the application
- Clean separation between UI components and data processing logic

## Data Flow

```
CSV Files → Statement Handlers → Processed DataFrame → 
                                   ↓
                 ┌─────────────────┴─────────────────┐
                 ↓                                   ↓
         Master CSV File                      Central Database
        (UK date format)                     (ISO date format)
```

## Best Practices

1. **Single Source of Truth** - Column names are defined in `FmColumnFormat` enum
2. **Clean Interfaces** - Components interact through well-defined interfaces
3. **Separation of Concerns** - UI logic is separate from data processing
4. **Consistent Error Handling** - Errors are logged and reported appropriately
5. **Format Flexibility** - Display formats can differ from storage formats

## Future Enhancements

Potential improvements to consider:

1. Transaction categorization rules
2. Enhanced search capabilities
3. Data visualization components
4. Export options for different formats
5. Backup and restore functionality
