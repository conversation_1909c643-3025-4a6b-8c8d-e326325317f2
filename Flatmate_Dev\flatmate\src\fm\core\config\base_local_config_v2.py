"""Enhanced Base Local Configuration Manager V2.

This is the next generation config system that combines:
- Override hierarchy from the original BaseLocalConfig
- Usage-based config definition (no speculation)
- Source tracking for debugging
- Simple string keys (no complex enums)
- Auto-generated documentation from actual usage

Philosophy:
- Config keys are defined WHERE they're used, not in separate files
- Only keys that are actually used exist in the system
- Discoverability through actual usage patterns
- Self-documenting code with source tracking
"""

import inspect
import logging
from abc import ABC
from pathlib import Path
from typing import Dict, Any, Optional

import yaml


class BaseLocalConfigV2(ABC):
    """Enhanced base configuration manager for components.
    
    Key improvements over V1:
    1. No speculative defaults - only keys that are ensure_defaults() exist
    2. Source tracking for debugging and documentation
    3. Simple string keys instead of complex enums
    4. Usage-based discoverability
    5. Maintains override hierarchy from V1
    
    Subclasses MUST define MODULE_NAME class attribute.
    
    Override Hierarchy (highest to lowest priority):
    1. User preferences (~/.flatmate/preferences.yaml)
    2. Component defaults (defaults.yaml)
    3. Runtime ensure_defaults() calls
    """
    
    # Subclasses MUST define this
    MODULE_NAME = None  # e.g., "categorize", "reports", "update_data"

    def __init__(self):
        """Initialize the enhanced config system."""
        # Enforce explicit module naming
        if not hasattr(self.__class__, 'MODULE_NAME') or self.__class__.MODULE_NAME is None:
            raise RuntimeError(
                f"{self.__class__.__name__} must define MODULE_NAME class attribute. "
                f"Example: MODULE_NAME = 'categorize'"
            )
        
        # Initialize storage
        self._config_values = {}
        self._key_origins = {}
        self._user_preferences = {}
        self._component_defaults = {}
        
        # Setup logging
        self.logger = logging.getLogger(f"{self.MODULE_NAME}.config")
        
        # Load override hierarchy
        self._load_override_hierarchy()
        
        # Log initialization
        self.logger.debug(f"{self.MODULE_NAME.title()} Configuration V2 initialized")

    def _load_override_hierarchy(self):
        """Load the config override hierarchy."""
        # Load component defaults from defaults.yaml (if exists)
        self._component_defaults = self._load_component_defaults()
        
        # Load user preferences (if exists)
        self._user_preferences = self._load_user_preferences()
        
        self.logger.debug(
            f"Loaded config hierarchy: "
            f"{len(self._component_defaults)} component defaults, "
            f"{len(self._user_preferences)} user preferences"
        )

    def _load_component_defaults(self) -> Dict[str, Any]:
        """Load component-specific defaults.yaml file."""
        defaults_path = self.get_defaults_file_path()
        if not defaults_path or not defaults_path.exists():
            return {}
        
        try:
            with open(defaults_path, "r", encoding="utf-8") as f:
                component_defaults = yaml.safe_load(f) or {}
            
            # Filter for this module's keys only
            module_defaults = {}
            prefix = f"{self.MODULE_NAME}."
            
            for key, value in component_defaults.items():
                if key.startswith(prefix):
                    module_defaults[key] = value
            
            self.logger.debug(f"Loaded {len(module_defaults)} component defaults from {defaults_path}")
            return module_defaults
            
        except Exception as e:
            self.logger.warning(f"Error loading component defaults from {defaults_path}: {e}")
            return {}

    def _load_user_preferences(self) -> Dict[str, Any]:
        """Load user preferences from ~/.flatmate/preferences.yaml."""
        prefs_path = Path("~/.flatmate/preferences.yaml").expanduser()
        if not prefs_path.exists():
            return {}
        
        try:
            with open(prefs_path, "r", encoding="utf-8") as f:
                all_prefs = yaml.safe_load(f) or {}
            
            # Filter for this module's keys only
            module_prefs = {}
            prefix = f"{self.MODULE_NAME}."
            
            for key, value in all_prefs.items():
                if key.startswith(prefix):
                    module_prefs[key] = value
            
            self.logger.debug(f"Loaded {len(module_prefs)} user preferences")
            return module_prefs
            
        except Exception as e:
            self.logger.warning(f"Error loading user preferences from {prefs_path}: {e}")
            return {}

    def get_defaults_file_path(self) -> Optional[Path]:
        """Get the path to the component's defaults.yaml file.
        
        Override this in subclasses to specify the correct path.
        Default assumes defaults.yaml is in the same directory as the config file.
        """
        return Path(__file__).parent / "defaults.yaml"

    def _apply_override_hierarchy(self, key: str, default_value: Any) -> Any:
        """Apply config override hierarchy for a key.
        
        Priority (highest to lowest):
        1. User preferences
        2. Component defaults
        3. Provided default value
        """
        # 1. Check user preferences first (highest priority)
        if key in self._user_preferences:
            return self._user_preferences[key]
        
        # 2. Check component defaults
        if key in self._component_defaults:
            return self._component_defaults[key]
        
        # 3. Use provided default (lowest priority)
        return default_value

    def ensure_defaults(self, defaults_dict: Dict[str, Any], source: str = None) -> None:
        """Enhanced ensure_defaults with override hierarchy and source tracking.

        Args:
            defaults_dict: Dictionary of default key-value pairs to ensure
            source: Optional source description (auto-detected if None)
        """
        # Auto-detect source from call stack if not provided
        if source is None:
            frame = inspect.currentframe().f_back
            filename = frame.f_code.co_filename
            line_number = frame.f_lineno
            function_name = frame.f_code.co_name

            # Create readable source description - only show from fm package root
            file_path = Path(filename)
            path_str = str(file_path)
            
            # Find 'fm' in the path and trim everything before it
            if '/fm/' in path_str:
                fm_index = path_str.find('/fm/')
                relative_path = path_str[fm_index + 1:]  # +1 to include the 'fm'
            elif '\\fm\\' in path_str:
                fm_index = path_str.find('\\fm\\')
                relative_path = path_str[fm_index + 1:]  # +1 to include the 'fm'
            else:
                # Fallback to just filename if fm not found
                relative_path = file_path.name
            
            source = f"{relative_path}:{function_name}():{line_number}"

        # Process each key with override hierarchy
        for key, default_value in defaults_dict.items():
            # Only set if the key doesn't already exist in our config
            if key not in self._config_values:
                # Apply override hierarchy to get the actual value
                final_value = self._apply_override_hierarchy(key, default_value)
                
                # Store the final value
                self._config_values[key] = final_value

                # Track where this key was set
                self._key_origins[key] = {
                    'source': source,
                    'default_value': default_value,
                    'final_value': final_value,
                    'module': self.MODULE_NAME,
                    'overridden': final_value != default_value
                }

                self.logger.debug(
                    f"Config key: {key} = {final_value} "
                    f"{'(overridden)' if final_value != default_value else '(default)'} "
                    f"from {source}"
                )

    def get_value(self, key: str, default: Any = None) -> Any:
        """Get a config value by key."""
        return self._config_values.get(key, default)
    
    def set_value(self, key: str, value: Any) -> None:
        """Set a config value by key."""
        self._config_values[key] = value

    def get_key_origins(self) -> Dict[str, Dict[str, Any]]:
        """Get information about where each config key was set."""
        return self._key_origins.copy()

    def generate_documented_yaml(self) -> str:
        """Generate YAML organized by source file and function/class."""
        lines = [
            f"# {self.MODULE_NAME.title()} Module Configuration", 
            "# Auto-generated from actual usage", 
            "# Organized by source file and function/class", 
            ""
        ]

        # Group keys by source file and function
        sources = {}
        
        for key, origin in self._key_origins.items():
            source = origin['source']
            value = origin['final_value']
            overridden = origin.get('overridden', False)
            
            # Parse source string
            if ':' in source:
                parts = source.split(':')
                file_part = parts[0]
                func_part = parts[1] if len(parts) > 1 else "unknown"
                
                # Clean up file path for display
                if file_part.startswith('fm/') or file_part.startswith('fm\\'):
                    clean_path = file_part.replace('\\', '/').replace('fm/', '')
                    if clean_path.endswith('.py'):
                        clean_path = clean_path[:-3]
                    filename = clean_path
                else:
                    filename = file_part.split('/')[-1].split('\\')[-1]
                    if filename.endswith('.py'):
                        filename = filename[:-3]
                
                # Clean up function name
                func_name = func_part.split('(')[0]
                
                if filename not in sources:
                    sources[filename] = {}
                if func_name not in sources[filename]:
                    sources[filename][func_name] = []
                
                sources[filename][func_name].append({
                    'key': key,
                    'value': value,
                    'overridden': overridden,
                    'default_value': origin['default_value']
                })
        
        # Generate organized YAML
        for filename in sorted(sources.keys()):
            display_name = filename if filename.endswith('.py') else f"{filename}.py"
            lines.append(f"# === {display_name} ===")
            lines.append("")
            
            for func_name in sorted(sources[filename].keys()):
                lines.append(f"# {func_name}:")
                
                # Group keys by namespace within this function
                func_keys = sources[filename][func_name]
                namespaces = {}
                
                for item in func_keys:
                    key = item['key']
                    value = item['value']
                    overridden = item['overridden']
                    
                    # Remove module prefix for cleaner YAML
                    clean_key = key
                    if key.startswith(f'{self.MODULE_NAME}.'):
                        clean_key = key[len(self.MODULE_NAME) + 1:]
                    
                    # Split into namespace parts
                    parts = clean_key.split('.')
                    if len(parts) > 1:
                        namespace = parts[0]
                        leaf_key = '.'.join(parts[1:])
                    else:
                        namespace = 'general'
                        leaf_key = clean_key
                    
                    if namespace not in namespaces:
                        namespaces[namespace] = {}
                    
                    # Handle nested keys
                    if '.' in leaf_key:
                        leaf_parts = leaf_key.split('.')
                        current = namespaces[namespace]
                        for part in leaf_parts[:-1]:
                            if part not in current:
                                current[part] = {}
                            current = current[part]
                        
                        # Add override info as comment
                        if overridden:
                            current[f"#{leaf_parts[-1]}_comment"] = f"Overridden from default: {item['default_value']}"
                        current[leaf_parts[-1]] = value
                    else:
                        if overridden:
                            namespaces[namespace][f"#{leaf_key}_comment"] = f"Overridden from default: {item['default_value']}"
                        namespaces[namespace][leaf_key] = value
                
                # Write the nested structure
                for namespace in sorted(namespaces.keys()):
                    lines.append(f"{namespace}:")
                    self._write_yaml_dict(namespaces[namespace], lines, indent=1)
                
                lines.append("")
            
            lines.append("")
        
        return "\n".join(lines)
    
    def _write_yaml_dict(self, data, lines, indent=0):
        """Helper to write nested dictionary as YAML."""
        indent_str = "  " * indent
        
        for key, value in sorted(data.items()):
            if key.startswith('#') and key.endswith('_comment'):
                # Skip comment keys in main output
                continue
            elif isinstance(value, dict):
                lines.append(f"{indent_str}{key}:")
                self._write_yaml_dict(value, lines, indent + 1)
            else:
                if isinstance(value, str):
                    lines.append(f"{indent_str}{key}: '{value}'")
                else:
                    lines.append(f"{indent_str}{key}: {value}")

    def save_defaults_yaml(self, filename: str = None) -> Path:
        """Save the generated YAML to a defaults file.

        The file is saved next to the inheriting config class, not the base class.
        Default filename is defaults.yaml (consistent with config.py)
        """
        if filename is None:
            filename = "defaults.yaml"

        yaml_content = self.generate_documented_yaml()

        # Save next to the defaults.yaml file (where the inheriting config lives)
        defaults_path = self.get_defaults_file_path()
        if defaults_path:
            test_file = defaults_path.parent / filename
        else:
            # Fallback to current directory if no defaults path
            test_file = Path.cwd() / filename

        with open(test_file, 'w') as f:
            f.write(yaml_content)

        self.logger.info(f"Defaults YAML saved to: {test_file}")
        return test_file
