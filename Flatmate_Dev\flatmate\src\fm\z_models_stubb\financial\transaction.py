from typing import Dict, Any, List
from datetime import datetime
from ..profiles.base_profile import BaseProfile

class Transaction(BaseProfile):
    """Class for managing financial transactions in the flatmate app."""
    
    def __init__(self, profile_id: str = None):
        super().__init__(profile_id)
        self.transaction_type: str = ""  # rent, utility, deposit, etc.
        self.amount: float = 0.0
        self.currency: str = "USD"
        self.date: str = datetime.now().isoformat()
        self.description: str = ""
        self.category: str = ""
        self.status: str = "pending"  # pending, completed, cancelled
        
        # Account information
        self.from_account_id: str = ""
        self.to_account_id: str = ""
        self.from_profile_id: str = ""  # ID of the profile making the payment
        self.to_profile_id: str = ""    # ID of the profile receiving the payment
        
        # Property related information
        self.property_id: str = ""  # Related property if applicable
        self.room_id: str = ""      # Related room if applicable
        
        # Additional details
        self.payment_method: str = ""
        self.receipt_url: str = ""
        self.notes: str = ""
        self.tags: List[str] = []
        self.attachments: List[str] = []  # URLs or paths to attached files
        
        # Recurring transaction details
        self.is_recurring: bool = False
        self.recurring_frequency: str = ""  # monthly, weekly, etc.
        self.recurring_end_date: str = ""
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the transaction to a dictionary."""
        data = super().to_dict()
        data.update({
            'transaction_type': self.transaction_type,
            'amount': self.amount,
            'currency': self.currency,
            'date': self.date,
            'description': self.description,
            'category': self.category,
            'status': self.status,
            'from_account_id': self.from_account_id,
            'to_account_id': self.to_account_id,
            'from_profile_id': self.from_profile_id,
            'to_profile_id': self.to_profile_id,
            'property_id': self.property_id,
            'room_id': self.room_id,
            'payment_method': self.payment_method,
            'receipt_url': self.receipt_url,
            'notes': self.notes,
            'tags': self.tags,
            'attachments': self.attachments,
            'is_recurring': self.is_recurring,
            'recurring_frequency': self.recurring_frequency,
            'recurring_end_date': self.recurring_end_date
        })
        return data
    
    def from_dict(self, data: Dict[str, Any]) -> None:
        """Load transaction data from a dictionary."""
        super().from_dict(data)
        self.transaction_type = data.get('transaction_type', '')
        self.amount = data.get('amount', 0.0)
        self.currency = data.get('currency', 'USD')
        self.date = data.get('date', datetime.now().isoformat())
        self.description = data.get('description', '')
        self.category = data.get('category', '')
        self.status = data.get('status', 'pending')
        self.from_account_id = data.get('from_account_id', '')
        self.to_account_id = data.get('to_account_id', '')
        self.from_profile_id = data.get('from_profile_id', '')
        self.to_profile_id = data.get('to_profile_id', '')
        self.property_id = data.get('property_id', '')
        self.room_id = data.get('room_id', '')
        self.payment_method = data.get('payment_method', '')
        self.receipt_url = data.get('receipt_url', '')
        self.notes = data.get('notes', '')
        self.tags = data.get('tags', [])
        self.attachments = data.get('attachments', [])
        self.is_recurring = data.get('is_recurring', False)
        self.recurring_frequency = data.get('recurring_frequency', '')
        self.recurring_end_date = data.get('recurring_end_date', '')
