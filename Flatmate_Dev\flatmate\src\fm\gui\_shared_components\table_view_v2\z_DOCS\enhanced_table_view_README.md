# Enhanced Table View System

Core table infrastructure for the Flatmate application, providing advanced features for displaying, filtering, and interacting with tabular data.

## Architecture

The system consists of three main components:

### 1. EnhancedTableModel (QStandardItemModel)
- Handles data storage and manipulation
- Supports editable/readonly columns
- Maintains original data for export/reset
- Provides type-aware sorting

### 2. EnhancedTableView (QTableView) 
- Core table display with advanced features
- Column management (resize, hide/show, reorder)
- Row selection and highlighting
- Context menus and keyboard shortcuts
- Export functionality (CSV, Excel)

### 3. EnhancedTableWidget (QWidget)
- Complete table solution with UI controls
- Filter toolbar (column selector, search, apply/clear)
- Action buttons (column visibility, export menu)
- Wraps EnhancedTableView with full user interface

## Features

### Data Management
- Pandas DataFrame integration
- Type-aware column handling
- Editable vs readonly column configuration
- Data validation and change tracking

### User Interface
- Resizable columns with memory
- Sortable columns (click headers)
- Movable columns (drag headers)
- Column visibility toggle
- Row selection (single/multi)
- Alternating row colors
- Context menus

### Filtering & Search
- Per-column filtering
- Global search across all columns
- Filter persistence
- Clear/reset functionality

### Export & Integration
- CSV export with current view state
- Excel export with formatting
- Integration with column manager system
- Configuration system integration

## Usage

### Basic Setup
```python
# Create complete table widget
table = EnhancedTableWidget()

# Set data
df = pd.DataFrame(your_data)
table.set_dataframe(df)

# Configure display
table.set_display_columns(['col1', 'col2'], {'col1': 'Column 1'})
table.set_editable_columns(['editable_col'])
table.set_column_widths({'Column 1': 20, 'Column 2': 30})

# Connect signals
table.table_view.row_selected.connect(handle_selection)
table.table_view.cell_edited.connect(handle_edit)
```

### Advanced Configuration
```python
# Just the table view (no toolbar)
table_view = EnhancedTableView()
table_view.set_dataframe(df)
table_view.set_editable_columns([2, 3])  # Column indices
```

## Signals

### EnhancedTableView emits:
- `row_selected(int)`: When a row is selected
- `cell_edited(int, int, str)`: When a cell is edited (row, col, new_value)

## Integration

- Works with `fm.core.standards` column manager
- Integrates with module config systems
- Supports `fm.core.services.logger` for debugging
- Compatible with pandas DataFrame workflows

## Performance

- Optimized for datasets up to 10,000 rows
- Lazy loading for large datasets (future enhancement)
- Efficient filtering using QSortFilterProxyModel
- Memory-conscious data handling

## Thread Safety

- **Not thread-safe** - use only from main GUI thread
- Data updates should be performed on main thread
- Use Qt signals for cross-thread communication

## Dependencies

- PySide6 (Qt6 Python bindings)
- pandas (DataFrame support)
- typing (type hints)

## Location

This is core table infrastructure located in:
`fm/gui/_shared_components/table_views/`

Used by multiple modules for consistent table display across the application.
