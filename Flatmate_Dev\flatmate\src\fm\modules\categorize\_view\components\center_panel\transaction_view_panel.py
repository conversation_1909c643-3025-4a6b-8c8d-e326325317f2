"""Transaction view panel for the Categorize module."""

import pandas as pd
from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import <PERSON><PERSON>oxLayout, QWidget
from fm.core.services.logger import Logger

from fm.gui._shared_components.table_view_v2.fm_table_view import CustomTableView_v2 as TableView
# Removed: from fm.core.standards.simple_column_manager import get_simple_column_manager
# Now using elegant ColumnNameService approach # ? should this be using simple collumn manager in standards or a module level class or function in utils?

from fm.modules.categorize.config import config

# Set up logger
logger = Logger()

class TransactionViewPanel(QWidget):
    """Transaction view panel with enhanced table functionality.

    This panel provides:
    - Enhanced table with filtering, column management, and export
    - Top bar with filter controls and column selection
    - Resizable columns with memory
    - Integration with the column manager system
    """
    
    # Signals
    transaction_selected = Signal(int)
    tags_updated = Signal(int, str)
    
    def __init__(self, parent=None):
        """Initialize the transaction view panel."""
        super().__init__(parent)
        logger.debug("Initializing TransactionViewPanel")
        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        """Initialize the UI components."""
        logger.debug("Setting up TransactionViewPanel UI")

        # Ensure required config defaults exist (no speculation!)
        config.ensure_defaults({
            'categorize.display.table_margin': 2,  # Reduced from 10 to 2
            'categorize.display.show_grid_lines': True,
            'categorize.display.row_height': 25,
            'categorize.display.default_visible_columns': ['date', 'details', 'amount', 'account', 'tags'],  # Pre-selected subset
            'categorize.display.available_columns': ['date', 'details', 'amount', 'account', 'tags', 'category', 'notes']  # All available
            # Note: Column widths now come from StandardColumns.get_standard_column_widths()
        })

        # Use config values
        margin = config.get_value('categorize.display.table_margin', 2)  # Reduced default from 10 to 2

        layout = QVBoxLayout(self)
        layout.setContentsMargins(margin, margin, margin, margin)

        # Create enhanced transaction table with filtering using v2 pattern
        logger.debug("Creating CustomTableView_v2 for transactions")
        self.transaction_table = TableView()

        # Configure the table with sensible defaults
        self.transaction_table.configure(
            auto_size_columns=True,
            max_column_width=40,
            editable_columns=['tags'],  # Only tags are editable
            show_toolbar=True,
            default_visible_columns=config.get_value('categorize.display.default_visible_columns', [])
        )

        layout.addWidget(self.transaction_table)

        logger.debug("TransactionViewPanel UI setup complete")

    def _connect_signals(self):
        """Connect internal signals."""
        logger.debug("Connecting TransactionViewPanel signals")
        # Connect row selection signal
        self.transaction_table.table_view.row_selected.connect(
            lambda row: self.transaction_selected.emit(row))
        
        # Connect cell edit signal
        self.transaction_table.table_view.cell_edited.connect(
            lambda row, col, value: self._handle_cell_edit(row, col, value))
        
        logger.debug("TransactionViewPanel signals connected")

    def _handle_cell_edit(self, row, col, value):
        """Handle cell edit events."""
        logger.debug(f"Cell edited: row={row}, col={col}, value={value}")
        
        # Get the column name from the model
        df = self.transaction_table.get_dataframe()
        if df.empty:
            return
            
        # Get the display columns from the model
        display_columns = self.transaction_table.model._display_columns
        if col >= len(display_columns):
            return
            
        col_name = display_columns[col]
        logger.debug(f"Edited column: {col_name}")
        
        # If this is the tags column
        if col_name == 'tags':
            # Get the transaction ID from the model
            if not df.empty and row < len(df):
                transaction_id = df.iloc[row].get('id', row)
                logger.debug(f"Tags updated for transaction {transaction_id}: {value}")
                self.tags_updated.emit(transaction_id, value)
    
    def set_transactions(self, df: pd.DataFrame):
        """Set the transactions dataframe to display - ELEGANT SOLUTION."""
        logger.debug(f"Setting transactions: {len(df) if df is not None else 0} rows")
        if df is not None and not df.empty:
            # Get standard column widths from ColumnNameService (these use display names)
            from fm.core.standards.column_name_service import ColumnNameService
            standard_widths = ColumnNameService.get_standard_column_widths()

            # Get default visible columns from config (using db_names)
            default_visible = config.get_value('categorize.display.default_visible_columns', None)

            # ELEGANT: Table view handles everything automatically!
            # The table will convert db_names to display_names and match column_widths accordingly
            self.transaction_table.configure(
                auto_size_columns=True,        # Enable auto-sizing with limit
                max_column_width=40,           # 40 character limit as requested
                column_widths=standard_widths, # Standard widths (display names)
                default_visible_columns=default_visible,  # db_names (will be converted)
                editable_columns=['Tags'],     # Display name for tags column
                show_toolbar=True
            ).set_dataframe(df).show()  # DataFrame with db_names -> automatically converted

            logger.debug("Transactions set successfully using ELEGANT ColumnNameService pattern")

    # Note: _apply_default_column_visibility and _apply_column_widths methods
    # are no longer needed - the new v2 pattern handles this in configure()

    def _save_column_selections(self):
        """Save current column selections to local config."""
        try:
            table_view = self.transaction_table.table_view
            model = table_view._model

            # Get currently visible columns (display names)
            visible_display_names = []
            for col_idx in range(model.columnCount()):
                if not table_view.isColumnHidden(col_idx):
                    col_name = model.headerData(col_idx, Qt.Horizontal)
                    visible_display_names.append(str(col_name))

            # Convert display names back to database names for storage using ColumnNameService
            from fm.core.standards.column_name_service import ColumnNameService
            # Get reverse mapping: display_name -> db_name
            display_to_db = ColumnNameService.get_reverse_mapping(
                # We need to provide the original db column names - get from the stored dataframe
                list(self.transaction_table._dataframe_db.columns) if hasattr(self.transaction_table, '_dataframe_db') else []
            )

            visible_db_names = []
            for display_name in visible_display_names:
                db_name = display_to_db.get(display_name)
                if db_name:
                    visible_db_names.append(db_name)
                else:
                    # Fallback: convert display name back to db format
                    visible_db_names.append(display_name.lower().replace(' ', '_'))

            # Save to config as last_used_columns (not overriding defaults)
            config.ensure_defaults({
                'categorize.display.last_used_columns': visible_db_names
            })

            logger.debug(f"Saved column selections: {visible_db_names}")

        except Exception as e:
            logger.error(f"Error saving column selections: {e}")

    def disconnect_signals(self):
        """Clean up signal connections."""
        logger.debug("Disconnecting TransactionViewPanel signals")
        if hasattr(self, 'transaction_table') and hasattr(self.transaction_table, 'table_view'):
            try:
                self.transaction_table.table_view.row_selected.disconnect()
                self.transaction_table.table_view.cell_edited.disconnect()
            except (TypeError, RuntimeError) as e:
                # Signal might not be connected
                logger.debug(f"Error disconnecting signals: {e}")








