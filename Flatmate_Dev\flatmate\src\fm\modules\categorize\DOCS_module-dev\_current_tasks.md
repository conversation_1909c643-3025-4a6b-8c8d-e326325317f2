# Current Tasks – Column Management & Config System

**✅ MAJOR PROGRESS: Default Columns System Working**
*Status: Core functionality implemented, debugging and refinement needed*

## � Current Priority Issues (Next Session)

- [ ] **DEBUG**: Column display name sourcing
  - Issue: "DEscription" vs "Details" - need to trace where display names come from
  - Location: Check `fm_standard_columns.py` vs `enhanced_columns.py` mapping
  - Goal: Ensure consistent naming from single source of truth

- [ ] **MISSING COLUMNS**: Not all database schema columns in dropdown
  - Issue: Column visibility dropdown missing some database columns
  - Need: Debug which columns are being filtered out by column manager
  - Check: `get_display_columns_for_module("categorize")` output

- [ ] **NO DEBUG OUTPUT**: Column selection changes not logged
  - Issue: No debug output when selecting/deselecting columns in dropdown
  - Need: Add logging to column visibility change handlers
  - Location: `EnhancedTableWidget._show_column_visibility()` method

- [ ] **NO CONFIG SAVING**: Column selections not persisted
  - Issue: Column changes not being saved to config
  - Need: Wire up `_save_column_selections()` to actual UI events
  - Goal: Save to local config (not user prefs unless explicitly set)

- [ ] **NO DEBUG OUTPUT**: Column selection changes not logged
  - Issue: No debug output when selecting/deselecting columns in dropdown
  - Need: Add logging to column visibility change handlers
  - Location: `EnhancedTableWidget._show_column_visibility()` method

- [ ] **NO CONFIG SAVING**: Column selections not persisted
  - Issue: Column changes not being saved to config
  - Need: Wire up `_save_column_selections()` to actual UI events
  - Goal: Save to local config (not user prefs unless explicitly set)

## ✅ Completed This Session (2025-06-17)

- [x] **Fixed Default Columns Configuration**
  - Added missing `ensure_defaults` call for `default_columns` in `TransactionViewPanel`
  - Config now properly defines: `['date', 'details', 'amount', 'account', 'tags']`

- [x] **Integrated Config with Column Manager**
  - Modified `column_preferences.py` to read from config system instead of hardcoded defaults
  - Added `_get_config_defaults_for_module()` method for categorize module

- [x] **Implemented Column Visibility Logic**
  - Created `_apply_default_column_visibility()` method
  - Properly maps database names to display names using column manager
  - Added debug logging to show available columns and visibility decisions

- [x] **Added Column Selection Persistence Framework**
  - Created `_save_column_selections()` method for saving to local config
  - Framework ready to be wired to UI events

- [x] **Fixed Logger System**
  - Corrected import to use bespoke logger (`fm.core.services.logger.Logger`)
  - All debug output now working properly

---

# Previous Sprint 1 Completed Items ✅

- [X] Date column: display date only (strip `00:00:00` time portion) ✅
- [X] Table: enable auto-resize and manual resize of columns ✅
- [X] Left panel buttons: use `OptionButton` styling from Update-Data module ✅
- [X] Design doc: add ergonomics section (table widget alternatives, category editing UX, search/filter features) ✅
  - Created `ergonomics_notes.md` with UI/UX recommendations
  - Added `cat_view_layout.md` with detailed layout specifications
  - Consolidated design in `mvp_overview.md`
- [X] Implement in-session file caching to preserve edits across navigation ✅
  - Created `cache_service.py` in `core/services` directory
  - Implemented in-memory caching with optional disk persistence
  - Integrated with categorize presenter to preserve transaction edits

## Sprint 2 (Core Functionality) - Prioritized

### Priority 1: Data Foundation

- [X] Extend data service:
  - [X] Add `tags` column to transactions table
  - [X] Create DB migration script
  - [X] Add date/account filters to transaction queries ✅

### Priority 2: Core UI Functionality

- [ ] Complete transaction table implementation:
  - [X] Make canonical columns read-only
  - [X] Add editable tags column
  - [X] Implement sorting and filtering capabilities ✅
  - [X] Add column visibility toggle ✅
  - [X] Create reusable EnhancedTableView component ✅
  - [ ] Add row highlighting on selection
  - [ ] Implement copy row functionality
  - [ ] Add export to CSV/Excel options

### Priority 3: Data Persistence

- [X] Implement caching service: ✅
  - [X] Create `CacheService` class in `fm.core.services` ✅
  - [X] Add in-memory storage with optional temp-file persistence ✅
  - [X] Integrate with presenter for preserving edits ✅

### Priority 4: Save & Validation

- [ ] Implement save & validation workflow:
  - [ ] Add "Save Tags" action for writing to DB
  - [ ] Add validation to prevent modifying canonical data
  - [ ] Prompt on unsaved changes when navigating away

### Priority 5: Advanced Features

- [ ] Create pattern editing UI:
  - [ ] Build interface for managing regex→tag rules
  - [ ] Implement load/save for pattern JSON
- [ ] Implement advanced filtering:
  - [X] Add date range picker ✅
  - [X] Add account selector ✅
  - [X] Add amount range filter ✅
  - [X] Add description search ✅
  - [X] Add tag filter ✅

### Priority 6: UI Refinements

- [ ] Improve table view ergonomics:
  - [ ] Add collapsible side panels
  - [ ] Implement dropdown for adding new categories
  - [ ] Add "Show All Columns" option
  - [ ] Standardize column order based on standards
  - [ ] Support for light/dark mode in table view

_Check off each box as the item is implemented and merged._
