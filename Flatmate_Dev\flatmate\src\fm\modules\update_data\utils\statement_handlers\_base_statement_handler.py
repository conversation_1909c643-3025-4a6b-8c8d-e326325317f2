"""Base class for bank statement type maps."""

import os.path
import re
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Tuple, Union

import pandas as pd
from fm.core.standards.fm_standard_columns import StandardColumns
from fm.core.utils.date_utils import convert_df_dates, standardize_date


class StatementHandler:
    @dataclass
    class StatementFormat:
        bank_name: str  # Bank name e.g., "Kiwibank"
        variant: str  # Format variant e.g., "basic"
        file_type: str  # File extension e.g., "csv"

    @dataclass
    class ColumnAttributes:
        n_source_cols: int = 0
        has_col_names: bool = False  # Whether the file has column names at all
        has_account_column: bool = False  # Whether account number is in source columns
        col_names_in_header: bool = (
            False  # Whether column names are in the header section
        )
        col_names_in_data_row: bool = False  # Whether column names are in a data row
        col_names_row: int = 0  # Which row contains the column names (if any)
        source_col_names: List[Union[str, int]] = field(
            default_factory=list
        )  # Allow both strings and ints
        target_col_names: List[Optional[StandardColumns]] = field(
            default_factory=list
        )  # Allow None
        date_format: str = ""  # Format string for date parsing (e.g., '%d/%m/%Y')

        @dataclass
        class ColumnHeaders:
            start_column: int = 0
            in_row: int = 0

    @dataclass
    class AccountNumberAttributes:
        pattern: str = ""

        in_data: bool = False  # Whether to check for account number in data
        location: Tuple[int, int] = field(default_factory=lambda: (0, 0))

        in_header: bool = False  # Whether to check the column name itself
        in_file_name: bool = False
        in_metadata: bool = False

    @dataclass
    class SourceMetadataAttributes:
        has_metadata_rows: bool = False
        metadata_start: Tuple[int, int] = field(default_factory=lambda: (0, 0))
        metadata_end: Tuple[int, int] = field(default_factory=lambda: (0, 0))

    @dataclass
    class FileInfo:
        """Information about a statement file"""

        bank_name: str
        account_number: str
        start_date: Optional[datetime]
        end_date: Optional[datetime]
        n_transactions: int
        source_filename: str

    statement_format: StatementFormat
    column_attrs: ColumnAttributes
    account_num_attrs: AccountNumberAttributes
    source_metadata_attrs: SourceMetadataAttributes

    """The header parameter in pd.read_csv() controls how column names are interpreted:
    header=None: No header row, uses default numeric column names
    header=0: First row is header (default)
    header=n: Use nth row as header"""

    def _check_account_number(self, df: pd.DataFrame, filepath: str) -> bool:
        """Check account number in order of reliability: metadata/data first, filename last"""
        # Import logging here to avoid circular imports
        from fm.core.services.logger import Logger

        logger = Logger()
        handler_name = self.__class__.__name__
        filename = os.path.basename(filepath)

        if not self.account_num_attrs.pattern:
            logger.debug(
                f"[{handler_name}] No account number pattern specified, skipping check"
            )
            return True

        logger.debug(
            f"[{handler_name}] Checking account number pattern '{self.account_num_attrs.pattern}' in file: {filename}"
        )

        # Check metadata first (most reliable)
        if self.account_num_attrs.in_metadata:
            row, col = self.account_num_attrs.location
            try:
                cell = df.iloc[row, col]
                logger.debug(
                    f"[{handler_name}] Checking metadata at [{row},{col}]: '{str(cell)}'"
                )
                if re.search(self.account_num_attrs.pattern, str(cell)):
                    logger.debug(f"[{handler_name}] Found account number in metadata")
                    return True
                logger.debug(f"[{handler_name}] Account number not found in metadata")
            except IndexError:
                logger.debug(
                    f"[{handler_name}] Metadata location [{row},{col}] out of bounds for DataFrame with shape {df.shape}"
                )

        # Then check data
        if self.account_num_attrs.in_data:
            row, col = self.account_num_attrs.location
            try:
                cell = df.iloc[row, col]
                logger.debug(
                    f"[{handler_name}] Checking data at [{row},{col}]: '{str(cell)}'"
                )
                if re.search(self.account_num_attrs.pattern, str(cell)):
                    logger.debug(f"[{handler_name}] Found account number in data")
                    return True
                logger.debug(f"[{handler_name}] Account number not found in data")
            except IndexError:
                logger.debug(
                    f"[{handler_name}] Data location [{row},{col}] out of bounds for DataFrame with shape {df.shape}"
                )

        # Then check header
        if self.account_num_attrs.in_header:
            logger.debug(
                f"[{handler_name}] Checking column headers: {[str(col) for col in df.columns]}"
            )
            if any(
                re.search(self.account_num_attrs.pattern, str(col))
                for col in df.columns
            ):
                logger.debug(f"[{handler_name}] Found account number in column headers")
                return True
            logger.debug(f"[{handler_name}] Account number not found in column headers")

        # Finally check filename (least reliable)
        if self.account_num_attrs.in_file_name:
            logger.debug(f"[{handler_name}] Checking filename: '{filename}'")
            # Use re.search instead of 'in' operator for regex patterns
            if re.search(self.account_num_attrs.pattern, filename):
                logger.debug(f"[{handler_name}] Found account number in filename")
                return True
            logger.debug(f"[{handler_name}] Account number not found in filename")

        # No valid account number found in any location
        logger.debug(f"[{handler_name}] No valid account number found in any location")
        return False

    def matches_statement_type(self, df: pd.DataFrame) -> bool:
        handler_name = self.__class__.__name__
        filename = df.filename

        # Import logging here to avoid circular imports
        from fm.core.services.logger import Logger

        logger = Logger()

        logger.debug(f"[{handler_name}] Checking if file matches: {filename}")
        logger.debug(
            f"[{handler_name}] Expected columns: {self.column_attrs.n_source_cols}, Actual: {len(df.columns)}"
        )

        # Check number of columns
        if len(df.columns) != self.column_attrs.n_source_cols:
            logger.debug(
                f"[{handler_name}] Column count mismatch: expected {self.column_attrs.n_source_cols}, got {len(df.columns)}"
            )
            return False

        # Check column names if specified and file should have column names
        if self.column_attrs.has_col_names:
            if self.column_attrs.col_names_in_header:
                # Names should be in column headers in order
                expected = [str(name) for name in self.column_attrs.source_col_names]
                actual = [str(col) for col in df.columns]
                logger.debug(
                    f"[{handler_name}] Checking column headers - Expected: {expected}"
                )
                logger.debug(
                    f"[{handler_name}] Checking column headers - Actual: {actual}"
                )
                if expected != actual:
                    logger.debug(f"[{handler_name}] Column headers don't match")
                    return False

            elif self.column_attrs.col_names_in_data_row:
                # Names should be in specified row in order
                row = df.iloc[self.column_attrs.col_names_row]
                expected = [str(name) for name in self.column_attrs.source_col_names]
                actual = [str(cell) for cell in row]
                logger.debug(
                    f"[{handler_name}] Checking data row {self.column_attrs.col_names_row} - Expected: {expected}"
                )
                logger.debug(
                    f"[{handler_name}] Checking data row {self.column_attrs.col_names_row} - Actual: {actual}"
                )
                if expected != actual:
                    logger.debug(f"[{handler_name}] Data row column names don't match")
                    return False

        # Check account number in any valid location
        logger.debug(
            f"[{handler_name}] Checking account number pattern: {self.account_num_attrs.pattern}"
        )
        if not self._check_account_number(df, df.filepath):
            logger.debug(f"[{handler_name}] Account number check failed")
            return False

        logger.debug(f"[{handler_name}] File matches handler criteria: {filename}")
        return True

    def get_file_info(self, df: pd.DataFrame) -> FileInfo:
        """Extract key information about the statement file"""
        # Find which source column maps to DATE
        date_index = None
        for i, target_col in enumerate(self.column_attrs.target_col_names):
            if target_col == StandardColumns.DATE:
                date_index = i
                break

        # Get date range from raw data
        if date_index is not None:
            # Skip metadata if present
            start_row = (
                self.source_metadata_attrs.metadata_end[0] + 1
                if self.source_metadata_attrs.has_metadata_rows
                else 0
            )
            # Skip header row if column names are in data
            if self.column_attrs.col_names_in_data_row:
                start_row = max(start_row, self.column_attrs.col_names_row + 1)

            dates = pd.to_datetime(df.iloc[start_row:, date_index])
            start_date = dates.min() if not dates.empty else None
            end_date = dates.max() if not dates.empty else None
        else:
            start_date = None
            end_date = None

        # Extract account number from the DataFrame
        account_number = self._extract_account_number(df)

        return self.FileInfo(
            bank_name=self.statement_format.bank_name,
            account_number=account_number,
            start_date=start_date,
            end_date=end_date,
            n_transactions=(len(df) - start_row if date_index is not None else 0),
            source_filename=df.filename,
        )

    def _reorder_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Reorder columns based on StandardColumns enum order, if they exist"""
        # Import logging here to avoid circular imports
        from fm.core.services.logger import Logger

        logger = Logger()
        handler_name = self.__class__.__name__

        # Get the standard order from enum
        standard_order = [fmt.value for fmt in StandardColumns]

        logger.debug(f"[{handler_name}] Standard column order: {standard_order}")
        logger.debug(f"[{handler_name}] Current columns: {list(df.columns)}")

        # Filter to only columns that exist in df
        ordered_cols = [col for col in standard_order if col in df.columns]

        # Add any remaining columns that aren't in the standard order
        remaining_cols = [col for col in df.columns if col not in ordered_cols]
        ordered_cols.extend(remaining_cols)

        logger.debug(f"[{handler_name}] Reordered columns: {ordered_cols}")

        # Use reindex to ensure we return a DataFrame
        return df.reindex(columns=ordered_cols)

    def _custom_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """Hook for handlers to perform bank-specific formatting
        Override this in bank handlers that need custom formatting"""
        # Standardize dates if a date format is specified
        if self.column_attrs.date_format:
            self._standardize_dates(df)
        return df

    def _standardize_dates(self, df: pd.DataFrame) -> None:
        """Standardize dates in the DataFrame to ISO format.

        Uses the date_format specified in column_attrs to parse dates
        and convert them to ISO 8601 format (YYYY-MM-DD).

        Args:
            df: DataFrame with dates to standardize
        """
        # Import logging here to avoid circular imports
        from fm.core.services.logger import Logger

        logger = Logger()
        handler_name = self.__class__.__name__
        date_col = StandardColumns.DATE.value

        if date_col not in df.columns:
            logger.debug(f"[{handler_name}] No date column found for standardization")
            return

        # Get sample of dates before standardization
        sample_dates = df[date_col].head(3).tolist()
        logger.debug(
            f"[{handler_name}] Sample dates before standardization: {sample_dates}"
        )

        # Track original null count
        null_before = df[date_col].isna().sum()

        # Apply standardization to each date in the column
        if self.column_attrs.date_format:
            logger.debug(
                f"[{handler_name}] Standardizing dates using format: {self.column_attrs.date_format}"
            )
            # Use the date_utils function with the handler's date format
            # First try with the handler's specific format
            original_df = df.copy()

            # Create a custom DATE_FORMATS list with the handler's format first
            from fm.core.utils.date_utils import DATE_FORMATS

            custom_formats = [self.column_attrs.date_format] + [
                fmt for fmt in DATE_FORMATS if fmt != self.column_attrs.date_format
            ]

            # Try each date in the column with the handler's format first
            for idx, date_val in enumerate(df[date_col]):
                if pd.isna(date_val):
                    continue

                # Try the handler's format first
                try:
                    date_obj = datetime.strptime(
                        str(date_val).strip(), self.column_attrs.date_format
                    )
                    df.at[idx, date_col] = date_obj.strftime("%Y-%m-%d")  # ISO format
                except ValueError:
                    # If that fails, let the standard function try all formats
                    df.at[idx, date_col] = standardize_date(date_val)

            # Log success rate
            success_count = (df[date_col].notna() & original_df[date_col].notna()).sum()
            total_dates = original_df[date_col].notna().sum()
            if total_dates > 0:
                success_rate = (success_count / total_dates) * 100
                logger.debug(
                    f"[{handler_name}] Date conversion success rate: {success_rate:.1f}% ({success_count}/{total_dates})"
                )

            # Check if any dates couldn't be converted (became null)
            null_after = df[date_col].isna().sum()
            if null_after > null_before:
                logger.debug(
                    f"[{handler_name}] WARNING: {null_after - null_before} dates could not be converted to ISO format"
                )

    def format_df(self, df: pd.DataFrame) -> pd.DataFrame:
        """Format the DataFrame to standard format"""
        # Import logging here to avoid circular imports
        from fm.core.services.logger import Logger

        logger = Logger()
        handler_name = (
            self.__class__.__name__
        )  # todo Should this be self.statement_format.bank_name,variant,file_type or key values? purpose of this line?

        # 1. Extract account number BEFORE stripping metadata if needed
        account_number = None
        # skip_first_row = False #todo purpose of this line?
        if not self.column_attrs.has_account_column:
            # Extract account number and check if we found it in the first row
            account_number = self._extract_account_number(df)
            logger.debug(f"[{handler_name}] Extracted account number: {account_number}")

        # 2. Strip metadata if present
        if self.source_metadata_attrs.has_metadata_rows:
            start = self.source_metadata_attrs.metadata_end[0] + 1
            logger.debug(f"[{handler_name}] Stripping metadata rows 0-{start-1}")
            df = df.iloc[start:]
        """# Skip first row if we found account number there and it's not already handled as metadata
        elif skip_first_row:
            logger.debug(
                f"[{handler_name}] Skipping first row with account number"
            )
            df = df.iloc[1:]"""

        # 3. Handle column names in data
        if self.column_attrs.col_names_in_data_row:
            df.columns = df.iloc[self.column_attrs.col_names_row]
            df = df.iloc[self.column_attrs.col_names_row + 1 :]

        # 4. Map source columns to target format
        target_col_values = [
            name.value
            for name in self.column_attrs.target_col_names
            if name is not None
        ]

        # Log column mapping for debugging
        logger.debug(
            f"[{handler_name}] Mapping columns: {list(df.columns)} -> {target_col_values}"
        )
        logger.debug(f"[{handler_name}] DataFrame shape: {df.shape}")

        # 5. Assign column names - no need for column count checks
        df.columns = target_col_values

        # 6. Add source filename
        df[StandardColumns.SOURCE_FILENAME.value] = df.filename

        # 7. Add account number if it was extracted earlier
        if not self.column_attrs.has_account_column and account_number is not None:
            df[StandardColumns.ACCOUNT.value] = account_number

        # 8. Allow handler to perform custom formatting
        df = self._custom_format(df)

        # 9. Automatically remove empty columns
        if StandardColumns.EMPTY_COLUMN.value in df.columns:
            df = df.drop(columns=[StandardColumns.EMPTY_COLUMN.value])
            logger.debug(f"[{handler_name}] Removed empty column")

        # 10. Reorder columns to standard format
        df = self._reorder_columns(df)

        return df

    def _extract_account_number(self, df: pd.DataFrame) -> str:
        """Hook for extract account number logic if no account column"""
        return ""
