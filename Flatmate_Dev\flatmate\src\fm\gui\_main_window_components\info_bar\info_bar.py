"""
InfoBar widget for displaying status messages across the application.
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import Q<PERSON>abel, QWidget, QHBoxLayout


class InfoBar(QWidget):
    """Widget for displaying status messages at the bottom of the application."""
    
    def __init__(self, parent=None):
        """Initialize the info bar widget."""
        super().__init__(parent)
        self._init_ui()
        
    def _init_ui(self):
        """Initialize the UI components."""
        # Set up the layout with minimal margins
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 3, 10, 3)
        layout.setSpacing(0)
        
        # Create the message label
        self.message_label = QLabel()
        self.message_label.setStyleSheet("color: #444; font-style: italic;")
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        # Add label to layout
        layout.addWidget(self.message_label)
        
        # Set fixed height and style with a subtle top border
        self.setFixedHeight(28)
        self.setStyleSheet("""
            background-color: #f8f8f8; 
            border-top: 1px solid #ddd;
        """)
        
    def set_message(self, message):
        """Set the message to display in the info bar.
        
        Args:
            message: The message text to display
        """
        self.message_label.setText(message)
        
        # Show the widget if there's a message, hide if empty
        self.setVisible(bool(message))
        
    def clear(self):
        """Clear the current message."""
        self.message_label.clear()
