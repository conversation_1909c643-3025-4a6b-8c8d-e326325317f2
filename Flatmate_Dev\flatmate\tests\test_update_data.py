#!/usr/bin/env python3
"""
Test script for the update data module.
"""

import sys
import os
import pytest
from unittest.mock import MagicMock, patch
import pandas as pd

# Add the project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout
from PySide6.QtCore import Qt

from src.flatmate.modules.update_data.ud_view import UpdateDataView
from src.flatmate.modules.update_data.ud_presenter import UpdateDataPresenter

@pytest.fixture
def app():
    """Fixture for QApplication instance."""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    return app

@pytest.fixture
def update_data_view(app):
    """Fixture for UpdateDataView instance."""
    view = UpdateDataView()
    return view

@pytest.fixture
def mock_dw_director():
    """Mock dw_director to avoid actual file operations."""
    with patch('src.flatmate.modules.update_data.ud_presenter.dw_director') as mock:
        mock.return_value = {
            "status": "success",
            "message": "Processing completed successfully",
            "details": {
                "input_files": 2,
                "output_rows": 100,
                "output_columns": 10,
                "backup_message": "Backups created"
            },
            "output_path": "/path/to/output.csv",
            "validation_results": None
        }
        yield mock

@pytest.fixture
def update_data_presenter(update_data_view, mock_dw_director):
    """Fixture for UpdateDataPresenter instance with mocked director."""
    presenter = UpdateDataPresenter(update_data_view)
    return presenter

def test_initial_state(update_data_view, update_data_presenter):
    """Test initial state of the update data module."""
    assert update_data_view is not None
    assert update_data_presenter is not None
    
    # Check initial UI state
    assert update_data_view.source_combo.currentText() == "Select Individual Files"
    assert update_data_view.save_combo.currentText() == "Same as Source"
    assert not update_data_view.process_btn.isEnabled()

def test_file_selection(update_data_view, update_data_presenter, monkeypatch):
    """Test file selection functionality."""
    # Mock QFileDialog.getOpenFileNames
    mock_files = ["/path/to/file1.csv", "/path/to/file2.csv"]
    monkeypatch.setattr(
        'PySide6.QtWidgets.QFileDialog.getOpenFileNames',
        lambda *args, **kwargs: (mock_files, None)
    )
    
    # Click the source select button
    update_data_view.source_select_btn.click()
    
    # Check if files are displayed
    assert "file1.csv" in update_data_view.source_display.text()
    assert "file2.csv" in update_data_view.source_display.text()
    
    # Process button should be enabled since we have source files
    assert update_data_view.process_btn.isEnabled()

def test_save_location_selection(update_data_view, update_data_presenter, monkeypatch):
    """Test save location selection functionality."""
    # Change save option to custom directory
    update_data_view.save_combo.setCurrentText("Select Directory")
    assert update_data_view.save_select_btn.isEnabled()
    
    # Mock QFileDialog.getExistingDirectory
    mock_dir = "/path/to/save/dir"
    monkeypatch.setattr(
        'PySide6.QtWidgets.QFileDialog.getExistingDirectory',
        lambda *args, **kwargs: mock_dir
    )
    
    # Click the save select button
    update_data_view.save_select_btn.click()
    
    # Check if directory is displayed
    assert "dir" in update_data_view.save_display.text()

def test_process_files(update_data_view, update_data_presenter, mock_dw_director, monkeypatch):
    """Test file processing functionality."""
    # Set up mock files
    mock_files = ["/path/to/file1.csv", "/path/to/file2.csv"]
    update_data_view.selected_sources = mock_files
    
    # Create mock DataFrame for preview
    mock_df = pd.DataFrame({
        'Date': ['2024-01-01', '2024-01-02'],
        'Amount': [100, 200],
        'Description': ['Test 1', 'Test 2']
    })
    
    # Mock pd.read_csv
    monkeypatch.setattr('pandas.read_csv', lambda *args, **kwargs: mock_df)
    
    # Enable process button
    update_data_view._update_process_button()
    assert update_data_view.process_btn.isEnabled()
    
    # Click process button
    update_data_view.process_btn.click()
    
    # Verify dw_director was called with correct job sheet
    mock_dw_director.assert_called_once()
    job_sheet = mock_dw_director.call_args[0][0]
    assert job_sheet["filepaths"] == mock_files
    assert job_sheet["source_files_type"] == "Select Individual Files"
    assert job_sheet["save_dir"] == "/path/to"

if __name__ == "__main__":
    pytest.main([__file__])
