"""
Simple column manager using only StandardColumns and database introspection.

This is the CORRECT implementation that follows user's architectural guidance:
- Uses StandardColumns as canonical source
- Gets available columns from database schema
- No speculative enhanced systems
"""

from typing import Dict, List, Optional
from .fm_standard_columns import StandardColumns


class SimpleColumnManager:
    """
    Simple column manager that uses StandardColumns and database introspection.
    
    This follows the user's explicit guidance to use fm_standard_columns as canonical.
    """
    
    def __init__(self):
        """Initialize the simple column manager."""
        self._db_repository = None  # Lazy-loaded database repository
    
    def _get_db_repository(self):
        """Lazy-load the database repository to avoid circular imports."""
        if self._db_repository is None:
            from fm.database_service.repository.sqlite_repository import SQLiteTransactionRepository
            self._db_repository = SQLiteTransactionRepository()
        return self._db_repository
    
    def get_available_columns_from_database(self, include_system_columns: bool = True) -> List[str]:
        """
        Get all available columns from the actual database schema.

        Args:
            include_system_columns: If True (default), includes system columns like id, import_date, modified_date

        Returns:
            List of database column names available in the transactions table
        """
        try:
            repository = self._get_db_repository()
            return repository.get_available_columns(include_system_columns=include_system_columns)
        except Exception as e:
            # Fallback to StandardColumns if database is not available
            from fm.core.services.logger import log
            log(f"Could not get columns from database: {e}. Using StandardColumns fallback.", level="warning")
            return [col.db_name for col in StandardColumns]
    
    def get_column_display_mapping(self, module_name: str = None) -> Dict[str, str]:
        """
        Get display names using StandardColumns as canonical source.
        
        Args:
            module_name: Module name (for future use, currently ignored)
            
        Returns:
            Dictionary mapping database column names to display names
        """
        available_columns = self.get_available_columns_from_database()
        mapping = {}
        
        # Use StandardColumns for known columns
        standard_mapping = StandardColumns.get_db_column_mapping()
        reverse_standard = {v: k for k, v in standard_mapping.items()}
        
        for db_col in available_columns:
            if db_col in reverse_standard:
                # Use StandardColumns display name
                mapping[db_col] = reverse_standard[db_col]
            else:
                # Fallback for non-standard columns (like category, notes, tags)
                mapping[db_col] = db_col.replace('_', ' ').title()
        
        return mapping
    
    def get_default_visible_columns(self, module_name: str) -> List[str]:
        """
        Get default visible columns for a module from config.
        
        Args:
            module_name: Name of the module
            
        Returns:
            List of database column names that should be visible by default
        """
        from fm.core.config.config import config
        config_key = f'{module_name}.display.default_visible_columns'
        return config.get_value(config_key, [])
    
    def prepare_dataframe_with_all_columns(self, df, module_name: str, include_system_columns: bool = True):
        """
        Prepare DataFrame with all available columns for display.

        Args:
            df: Input DataFrame
            module_name: Module name
            include_system_columns: If True (default), includes system columns like id, import_date, modified_date

        Returns:
            Tuple of (prepared_df, column_mapping, available_columns)
        """
        # Get all available columns from database
        available_columns = self.get_available_columns_from_database(include_system_columns=include_system_columns)

        # Get display mapping
        column_mapping = self.get_column_display_mapping(module_name)

        # Ensure DataFrame has all available columns
        df_copy = df.copy()
        for col in available_columns:
            if col not in df_copy.columns:
                df_copy[col] = ""  # Add missing columns with empty values

        # Reorder DataFrame to match available columns order
        df_ordered = df_copy[available_columns]

        return df_ordered, column_mapping, available_columns


# Global instance
_simple_column_manager = None


def get_simple_column_manager() -> SimpleColumnManager:
    """Get the global simple column manager instance."""
    global _simple_column_manager
    if _simple_column_manager is None:
        _simple_column_manager = SimpleColumnManager()
    return _simple_column_manager
