# MainWindow Refactor Discussion

_Last updated: 2025-06-19_

## 1. Purpose of this Document

This document captures the **current state** of `MainWindow`, the **issues** we have encountered (especially around the new custom title bar), and a set of **refactor options** to improve readability, maintainability, and separation of concerns.

## 2. Summary of Current Structure

`MainWindow` is responsible for many GUI concerns:

1. **Window configuration** (title, size, flags, animations, styles).
2. **Panel size persistence** (load / save).
3. **UI construction** – performed mainly in two private helpers:
   * `setup_ui()` – the public entry point
   * `_setup_central_widget()` – creates the central widget, main layout, custom title bar, content container & splitter placeholder.
4. **Splitter & panel management** – `_setup_splitter()`, `_configure_splitter_layout()`, animation helpers, etc.
5. **Runtime window events** like `resizeEvent`, `closeEvent`.

### Problems

* **God-method**: `_setup_central_widget()` mixes *layout creation*, *title-bar integration*, and *content area* concerns.
* **Naming**: The term *“central widget”* is Qt-specific, but our helper does more than that. The name does not reflect the breadth of responsibilities.
* **Title-bar coupling**: Adding the `CustomTitleBar` inside the same method made it harder to reason about padding, margins, and window flags.
* **Style leakage**: Some widgets set background colours, others rely on transparency, causing unexpected dark/ light bands.
* **Window flags timing**: Switching to `FramelessWindowHint` after the window is constructed can cause geometry glitches.

## 3. Title Bar Integration Challenges

| Symptom                                | Likely Cause                                                                                                 |
| -------------------------------------- | ------------------------------------------------------------------------------------------------------------ |
| Empty black band above/below title bar | Extra margins/ spacing on `main_layout` or `CustomTitleBar`; transparent vs. coloured backgrounds clash. |
| Title bar not flush with top edge      | Window flags set late, or central-widget margins present.                                                    |
| Drag area issues                       | `mouseMoveEvent` uses `self.parent` assumptions – ensure the parent is indeed the `MainWindow`.       |

## 4. Responsibilities – Proposed Separation

| Responsibility               | Current Owner                                   | Proposed Owner                                                          |
| ---------------------------- | ----------------------------------------------- | ----------------------------------------------------------------------- |
| Window flags + QoL attrs     | Mixed (`__init__`, `_setup_central_widget`) | **`__init__`** only (once, early)                               |
| Global window style          | `_apply_window_styles` (added)                | Keep here, but method name could be `_set_global_styles`              |
| Title bar creation           | `_setup_central_widget`                       | Move to dedicated `_create_title_bar()`                               |
| Content container & splitter | `_setup_central_widget`                       | `_create_content_area()`                                              |
| Panel setup & layout         | Multiple                                        | Keep but review names (e.g.,`_init_splitter`, `_populate_splitter`) |

## 5. Refactor Options

### Option A – **Light Touch**

* Keep existing function names but split `_setup_central_widget` into:
  * `_create_title_bar()` – returns the widget.
  * `_create_content_container()` – returns (container, layout).
* Keep call-sites inside the current function; reduces churn.

### Option B – **Moderate** (Recommended)

1. Rename `_setup_central_widget` ➜ `_setup_main_layout` (or similar).
2. Inside, call two helpers as above.
3. Move window-flag logic to `__init__`.
4. Ensure global styles are set once via `_apply_window_styles`.
5. Unit-test layout invariants (e.g., no margins at top, title bar height fixed).
   1. - possibly a good idea for now

### Option C – **Major Overhaul**

* Introduce a `MainWindowLayoutBuilder` helper class that encapsulates all widget creation and returns assembled widgets.
  * *this is ian inttersting idea I would like to explore, but option B seems best for now ....*
* Requires broader code changes; only consider if future layouts (e.g., detachable panes) are planned.
#### dev_notes:
  * *yes detachable pains would be great*
  * *the issue is that module views currently use a set_up_in_main_window method defined in main_window the module views inherit their interface methods from a base class in gui/shared/cmponents/base*

## 6. Immediate Action Items

1. **Agree on desired refactor level** (A/B/C).
2. **Audit margin & spacing** values on `main_layout`, `content_layout`, and `CustomTitleBar.main_layout`.
3. **Confirm window flag placement** in `__init__`.
4. **Remove redundant background colours** to rely on a single dark theme source.
5. **Write small tests** (manual or automated) verifying:
   * Title bar sits at `y = 0`.
   * No blank bands appear when resizing.

## 7. Open Questions

* Do we want the main window background to be fully transparent behind the title bar (for OS blur/shadows)?
  * *not a high priority*
  
* Should the title bar offer context-menu (right-click) actions similar to native windows?
  * *yes eventually but will defined at component level not a priority*

* Will future themes (light mode) require dynamic colour changes?
  * *yes in future this is planned*
* *How could we do option c without breaking everything  - the interface methods for the module views are dfined in a base class, so it mightnt be too difficult to adapt them... I could git commit and we could try it ...*
  *

---

_Please review this document and let me know which refactor option you'd like to pursue, or if further clarification is needed._
