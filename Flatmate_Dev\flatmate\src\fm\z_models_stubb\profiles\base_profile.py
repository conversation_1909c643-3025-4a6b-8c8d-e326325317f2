"""
Base profile implementation for the FlatMate application.
All profile types should inherit from this base class.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any, Optional
import json


@dataclass
class ContactInfo:
    """Basic contact information."""
    email: str = ""
    phone: str = ""
    address: str = ""


class BaseProfile(ABC):
    """Abstract base class for all profile types."""
    
    def __init__(self):
        self.created_at: datetime = datetime.now()
        self.updated_at: datetime = datetime.now()
        self.profile_type: str = self.__class__.__name__
        
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """Convert profile to dictionary format."""
        return {
            "profile_type": self.profile_type,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    @abstractmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseProfile':
        """Create profile instance from dictionary data."""
        pass
    
    def save(self, filepath: str) -> None:
        """Save profile to JSON file."""
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=4)
    
    @classmethod
    def load(cls, filepath: str) -> Optional['BaseProfile']:
        """Load profile from JSON file."""
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            return cls.from_dict(data)
        except FileNotFoundError:
            return None
