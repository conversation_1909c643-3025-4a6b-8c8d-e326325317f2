import os
from pathlib import Path
from datetime import datetime
from typing import Optional

class AppPaths:
    """
    Centralized path management for the application.
    
    All user-specific data is stored in ~/.flatmate/
    Project-specific files remain in the project directory.
    """
    
    # Project root (for non-user-specific files)
    PROJECT_ROOT = Path(__file__).resolve().parents[3]
    
    # User home directory
    USER_HOME = Path.home() / '.flatmate'
    
    # User-specific directories
    LOGS_DIR = USER_HOME / 'logs'
    CONFIG_DIR = USER_HOME / 'config'
    DATA_DIR = USER_HOME / 'data'
    CACHE_DIR = USER_HOME / 'cache'
    
    # Project directories (development)
    RESOURCES_DIR = PROJECT_ROOT / 'resources'
    UI_RESOURCES_DIR = PROJECT_ROOT / 'src' / 'fm' / 'gui' / 'resources'
    
    @classmethod
    def ensure_directories(cls):
        """
        Ensure all required application directories exist.
        Creates the following structure in ~/.flatmate/:
        - logs/      # Application logs
        - config/    # Configuration files
        - data/      # Application data
        - cache/     # Cached data
        """
        # Create all user directories
        for directory in [
            cls.USER_HOME,  # ~/.flatmate
            cls.LOGS_DIR,   # ~/.flatmate/logs
            cls.CONFIG_DIR, # ~/.flatmate/config
            cls.DATA_DIR,   # ~/.flatmate/data
            cls.CACHE_DIR,  # ~/.flatmate/cache
            
            # Project directories (for development)
            cls.RESOURCES_DIR,
            cls.UI_RESOURCES_DIR
        ]:
            try:
                cls.ensure_dir_exists(directory)
            except Exception as e:
                print(f"Warning: Could not create directory {directory}: {e}")
    
    @classmethod
    def ensure_dir_exists(cls, directory: Path):
        """Ensure a directory exists, creating it if necessary."""
        directory.mkdir(parents=True, exist_ok=True)
        return directory

    @classmethod
    def get_log_path(cls, log_name: Optional[str] = None) -> Path:
        """Generate a log file path."""
        cls.ensure_dir_exists(cls.LOGS_DIR)
        
        if log_name is None:
            log_name = f'flatmate_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        
        return cls.LOGS_DIR / log_name
    
    @classmethod
    def get_stylesheet_path(cls, theme_profile: Optional[str] = None) -> Path:
        """
        Get the path to the stylesheet based on theme profile.
        
        Args:
            theme_profile: Optional theme profile (e.g., 'dark', 'light')
        
        Returns:
            Path to the stylesheet
        """
        # Default to light theme if no profile specified
        theme_profile = theme_profile or 'light'
        
        stylesheet_path = cls.UI_RESOURCES_DIR / f'{theme_profile}_theme.qss'
        
        if not stylesheet_path.exists():
            # Fallback to light theme if specified theme doesn't exist
            stylesheet_path = cls.UI_RESOURCES_DIR / 'light_theme.qss'
        
        return stylesheet_path
