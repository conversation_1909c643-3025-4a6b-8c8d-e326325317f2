# FlatMate App

A desktop application for managing accounts.
Parsing bank statements (CSV), categorising transactions, and formating data for display and file output. 

Originally conceieved for as a personal project to help manage shared living expenses. (Managing rent and bills accounts for a shared tenancy)

# I split the app into 3 main areas for the purposes of development

- Import data / update data base
- Profiles (the information used to structure the data)
- View Display and output.

## There are also stubs for:
- Categorise (including auto categorisation, it should learn and make guesses)
- Onboarding (Ask a bunch of questions and to build a profile)
- Receipt parsing - (currently using mindee, but it could be any document recognition service)

The first thing I built was 

# statement parsing logic for csvs,
and a pipeline to merger them and drop duplicates. (<PERSON>das)
I got this to work succesfully pretty quickly.

But who knew GUIs were such an incredible challenge?
I wanted a polymorphic UI, that changes depending on task.
with context specific options and settings.
I want this to be usable by non technical people.

I started with building a basic 3 panel GUI, a center panel with two wings.
And a home module, from which to navigate to other modules.

This is basically an empty host.. with methods.

It now has a side_bar with its own widgets like the NavPane
and an a UtilitiesPane (which currently contains nothing but a settings icon)

I adapted my CSV merger to "update_data" module - this  is the module that handles the import of data, and the updating of the database. 
It is the most developed.
It has been through many iterations and now has a "statement handler" system. 
This is so the user doesnt have to tell the system what sort of bank statement it is.

The system is designed to recognise the formats of various CSV statements issued by NZ banks, convert them to a standardised format, and merge them, removing any duplicate records.

I have recently implemented an sql database.
This is entirely AI generated code. 

The gui still reflects the original CSV - dataframe - csv design. 

My current primary concerns are, a settings panel incliding dev_settings for ease of testing and refining the design.

A clear system for adding widgets using base classes and QT's .qss system. 

a tidy system of generic widget classes and components for use throughout, organised in a structured way in fm/gui/components

And the module transition system, which has recently undergone a revamp. 

This module transition system was built some months ago.
It was basically hacked together with the help of AI. 
It has a complex set of interelating dependancies and direct method calls. 

The goal is to simplify this system and to use an even based protocol.

We have a module register, that (I think) core.module_coordinator should use to import modules. 
There should be an agreed name mapping for modules, and an agreed structure for the calls. 

Main window has methods that have to be called directly. 
So cordinator will have to have access to main window also. 

While I think an event system could be ideal, I don't see how the system can ultimately work without direct method calls.

At the moment the nav_pane sends it's calls up through various container widgets to main window, which seems totally daft
to me. I want to simplify this system, at the moment its finicky and complex and adding new features is a PITA

Mostly I just want to implement the settings panel
with dev options
so I can get on with tesitng and refining the database and statement handlers, and adding actual functionality!

But as it's such a pita, I came to the realisation that I probably need to rationalise this module transition system ....
(Although the new nav pane now works at least, even if the "event based system, is more or less a facade)

Any feature I add, is going to have to negotiate this system, and it cant be as much of a PITA as it is now to do this.

(Nav_pane was a nightmre)
(how do I now get the settings icon to open the right_panel with contect specific options for update data? How do I seperate dev options from user preferences?)


The AI has  a habit of over complicating things,
Not being much of a programmer.. 
I favour readable self documenting code. 

This modual transition issue is going to take some thought... 

The small amount of info I hae been able to find on similar systems is not well explained.

this is where diagrams and schematics could come in handy, some visual mapping...

Any suggestions are welcome!







 




