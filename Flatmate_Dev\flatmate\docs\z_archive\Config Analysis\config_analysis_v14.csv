Category,Config Type,Module,Parameter,Type,Default,Required,Description,Source File,Restart Required,Scope
,,,,,,,,,,
# App Environment (System-wide settings),,,,,,,,,,
env,init,app,env.mode,str,development,yes,Environment mode (dev/prod),config/env.py,yes,system
env,init,app,env.debug,bool,FALSE,no,Show debug information,config/env.py,yes,system
env,init,app,env.os_type,str,auto-detect,yes,Operating system (windows/mac/linux),config/env.py,yes,system
,,,,,,,,,,
# Core App Paths,,,,,,,,,,
sys,init,app,paths.project_root,Path,.,yes,Project root directory,core/config/paths.py,yes,system
sys,init,app,paths.logs,Path,PROJECT_ROOT/logs,yes,Log files directory,core/config/paths.py,yes,system
sys,init,app,paths.config,Path,PROJECT_ROOT/configs,yes,Config files directory,core/config/paths.py,yes,system
sys,init,app,paths.resources,Path,PROJECT_ROOT/resources,yes,Resources directory,core/config/paths.py,yes,system
,,,,,,,,,,
# Flat Manager Data Paths,,,,,,,,,,
sys,init,app,paths.user_home,Path,~/.flatmate,yes,Flat manager data directory,core/config/paths.py,yes,system
sys,init,app,paths.data,Path,~/.flatmate/data,yes,Bank data directory,core/config/paths.py,yes,system
sys,init,app,paths.profiles,Path,~/.flatmate/profiles,yes,Flatmate/landlord profile storage,core/config/paths.py,yes,system
sys,init,app,paths.reports,Path,~/.flatmate/reports,yes,Generated reports directory,core/config/paths.py,yes,system
,,,,,,,,,,
# Bank Data Management,,,,,,,,,,
sys,init,update_data,paths.master,Path,${paths.data}/master,yes,Master bank file location,modules/update_data/config/ud_config.py,yes,system
sys,init,update_data,paths.backup,Path,${paths.data}/backup,yes,Bank file backups,modules/update_data/config/ud_config.py,yes,system
sys,runtime,update_data,paths.recent_masters,list,[],no,Recent bank files,modules/update_data/config/ud_config.py,no,user
sys,runtime,update_data,paths.master_history,dict,{},no,Bank file processing history,modules/update_data/config/ud_config.py,no,user
,,,,,,,,,,
# Report Settings,,,,,,,,,,
sys,runtime,app,reports.last_output_path,Path,~/Documents,no,Last used report output location,core/config/paths.py,no,user
sys,runtime,app,reports.recent_outputs,list,[],no,Recent report output locations,core/config/paths.py,no,user
,,,,,,,,,,
# Flat Manager UI Preferences,,,,,,,,,,
ui,runtime,app,window.left_panel_width,int,250,no,Left panel width,gui/main_window.py,no,user
ui,runtime,app,window.right_panel_width,int,250,no,Right panel width,gui/main_window.py,no,user
ui,runtime,app,window.column_widths,dict,{},no,Table column widths,gui/main_window.py,no,user
ui,runtime,app,window.recent_files,list,[],no,Recent files,gui/main_window.py,no,user
,,,,,,,,,,
# Flat Manager Logging Preferences,,,,,,,,,,
log,runtime,app,log.show_info,bool,TRUE,no,Show info messages,core/logger.py,no,user
log,runtime,app,log.show_warnings,bool,TRUE,no,Show warning messages,core/logger.py,no,user
,,,,,,,,,,
# UI Theme Constants,,,,,,,,,,
const,init,app,theme.colors.primary,str,#3B8A45,yes,Primary theme color,gui/styles/constants.py,yes,const
const,init,app,theme.colors.background,str,#2C2C2C,yes,Background color,gui/styles/constants.py,yes,const
const,init,app,theme.font.size,int,12,yes,Base font size (requires app restart),gui/styles/constants.py,yes,const
,,,,,,,,,,
# Data Format Constants,,,,,,,,,,
const,init,update_data,formats.date_format,str,%d/%m/%Y,yes,Bank date format,modules/update_data/utils/formatting/formats/bank_format.py,yes,const