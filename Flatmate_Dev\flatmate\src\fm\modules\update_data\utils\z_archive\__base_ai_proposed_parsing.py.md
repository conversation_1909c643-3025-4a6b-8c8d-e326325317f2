
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional, Dict

class TransactionType(Enum):
    DEBIT = 'debit'
    CREDIT = 'credit'
    TRANSFER = 'transfer'
    # Add more as needed

@dataclass
class StandardTransaction:
    # Universal transaction record
    unique_id: str  # Guaranteed unique across all sources
    date: datetime
    amount: Decimal
    payee: str
    description: str
    transaction_type: TransactionType
    account_id: str
    raw_source_data: Dict[str, Any]  # Preserve original data

class BankStatementParser:
    # Base class for bank-specific parsers
    def detect_format(self, file_path: str) -> bool:
        # Implement format detection logic
        pass

    def parse(self, file_path: str) -> List[StandardTransaction]:
        # Transform bank-specific format to standard transactions
        pass

class StatementTypeRegistry:
    # Manage different bank statement parsers
    _parsers: List[BankStatementParser] = []

    @classmethod
    def register(cls, parser: BankStatementParser):
        cls._parsers.append(parser)

    @classmethod
    def detect_and_parse(cls, file_path: str) -> List[StandardTransaction]:
        for parser in cls._parsers:
            if parser.detect_format(file_path):
                return parser.parse(file_path)
        raise ValueError(f"No parser found for {file_path}")

class TransactionRepository:
    # Manage storage and updates of transactions
    def upsert(self, transactions: List[StandardTransaction]):
        # Insert or update transactions
        # Implement deduplication logic
        pass

    def get_by_account(self, account_id: str) -> List[StandardTransaction]:
        pass

# Example bank-specific parser
class ASBBankParser(BankStatementParser):
    def detect_format(self, file_path: str) -> bool:
        # Specific ASB bank format detection
        pass

    def parse(self, file_path: str) -> List[StandardTransaction]:
        # Transform ASB bank CSV to standard transactions
        pass

# Registration
StatementTypeRegistry.register(ASBBankParser())-