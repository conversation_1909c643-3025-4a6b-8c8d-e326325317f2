import re
from dataclasses import dataclass
from typing import ClassVar

import pandas as pd

from ._base_statement_handler import StatementHandler
from fm.core.standards.fm_standard_columns import StandardColumns


@dataclass
class CoopStandardCSVHandler(StatementHandler):
    # Class level attributes
    name: ClassVar[str] = "Co-op Bank Standard CSV"
    file_extension: ClassVar[str] = ".csv"

    def __post_init__(self):
        # Column mapping
        self.column_attrs = self.ColumnAttributes(
            has_col_names=True,  # File has column names
            has_account_column=False,  # Account number is in filename
            col_names_in_header=True,  # Column names are in the header section
            source_col_names=["Date", "Details", "Amount", "Balance"],
            target_col_names=[
                StandardColumns.DATE,
                StandardColumns.DETAILS,
                StandardColumns.AMOUNT,
                StandardColumns.BALANCE,
                StandardColumns.ACCOUNT,
            ],
            n_source_cols=4,  # Must have Date, Details, Amount, Balance
            date_format='%d/%m/%Y',  # Co-op uses DD/MM/YYYY format (verified with example files)
        )

        # ? add:
        # self.metadata_cols = processing_tracker.metadata_column_tracker.get()
        #  self.n_expected_cols = self.column_attrs.n_source_cols + len(self.metadata_cols)

        #
        # Account number is in filename
        self.account_num_attrs = self.AccountNumberAttributes(
            pattern=r"02-\d{4}-\d{7}-\d{3}.*",  # Co-op format: 02-XXXX-XXXXXXX-XXX_date
            in_file_name=True,  # Check filename
        )

        # No metadata
        self.source_metadata_attrs = self.SourceMetadataAttributes(
            has_metadata_rows=False,
        )

        # Statement type
        self.statement_format = self.StatementFormat(
            bank_name="Co-operative Bank",
            variant="standard",
            file_type="csv",
        )

    def _extract_account_number(self, df: pd.DataFrame) -> str:
        """Extract Co-op-specific account number from filename.

        Args:
            df: DataFrame containing the data with filepath attribute

        Returns:
            Account number as a string, or empty string if not found
        """
        # For Co-op handler, we know account number is always in the filename
        match = re.search(self.account_num_attrs.pattern, df.filename)
        if match:
            return match.group(0)
        return ""
