"""
Data module for transaction storage and retrieval.
Provides a clean interface for working with transaction data.
"""
from .repository.transaction_repository import ImportResult, Transaction, TransactionRepository
from .repository.sqlite_repository import SQLiteTransactionRepository
from .service import DataService
from .events import event_bus, DataEvents
from .converters import CSVToTransactionConverter, TransactionToCSVConverter

# Create a singleton instance for application-wide use
data_service = DataService()

__all__ = [
    'Transaction',
    'ImportResult',
    'TransactionRepository',
    'SQLiteTransactionRepository',
    'DataService',
    'data_service',
    'event_bus',
    'DataEvents',
    'CSVToTransactionConverter',
    'TransactionToCSVConverter'
]
