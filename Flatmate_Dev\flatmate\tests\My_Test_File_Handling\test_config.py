from dataclasses import dataclass
import os
import pandas as pd

TEST_DIR_PATH = os.path.join(os.path.dirname(__file__),"tests/test_data")

class TestConfig:
    def __init__(self) -> None:
        self.TEST_DIR_PATH = TEST_DIR_PATH
        
    
    def get_test_dfs(self):
        # List all files in the test directory
        all_files = os.listdir(TEST_DIR_PATH)
        
        # Filter for CSV files case-insensitively
        csv_files = [f for f in all_files if f.lower().endswith('.csv')]
        
        # Read CSV files into DataFrames
        test_dfs = []
        for file in csv_files:
            test_dfs.append(pd.read_csv(os.path.join(TEST_DIR_PATH, file)))