#!/usr/bin/env python3
"""
Director module for coordinating the data processing pipeline.
"""

import datetime
import os
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from fm.core.services.logger import log
from fm.core.standards.fm_standard_columns import StandardColumns
from fm.core.utils.date_utils import convert_df_dates

# Data service and converter imports for database updates
from fm.database_service.converters import CSVToTransactionConverter
from fm.database_service.service import DataService
from fm.database_service.repository.transaction_repository import ImportResult

from ..config.ud_config import ud_config
from ..services.events import UpdateDataEventService

from .dw_pipeline import (
    load_csv_file,
    back_up_originals,
    merge_dataframes,
    move_to_unrecognised,
    process_with_handler,
    save_master_file,
    update_database_records,
    validate_core_data_structure,
)
from .processing_tracker import (
    clear_all_trackers,
    processed_files_tracker,
    unrecognised_files_tracker,
)


# Custom exceptions
class FileBackupError(Exception):
    """Exception raised when file backup fails."""

    def __init__(self, message, details=None):
        super().__init__(message)
        self.details = details


class DatabaseUpdateError(Exception):
    """Exception raised when database update fails."""

    def __init__(self, message, details=None):
        super().__init__(message)
        self.details = details


def dw_director(job_sheet: Dict[str, Any]) -> Dict[str, Any]:
    """Orchestrates the bank statement processing pipeline.

    Args:
        job_sheet: Dictionary containing filepaths, save_folder, and update_database flag

    Returns:
        Dictionary with processing results and statistics
    """
    # Clear processing trackers
    clear_all_trackers()

    # Unpack job_sheet
    filepaths = job_sheet["filepaths"]
    save_folder = job_sheet["save_folder"]
    update_database = job_sheet.get("update_database", False)

    # Publish processing started event
    UpdateDataEventService.publish_processing_started(job_sheet)

    try:
        # Process all files - flow logic now in director
        # Step 1: Load all CSV files
        loaded_dfs = []
        unrecognized_files = []

        for filepath in filepaths:
            try:
                df = load_csv_file(filepath)
                if df is not None:
                    loaded_dfs.append(df)
                    # Only mark as unrecognized if we can't find a handler for it
                    # We'll check this in the next step
                else:
                    log(f"Failed to load file: {filepath}", level="warning")
                    unrecognized_files.append(
                        {"filepath": filepath, "reason": "Failed to load"}
                    )
            except Exception as e:
                log(f"Error loading file {filepath}: {str(e)}", level="error")
                unrecognized_files.append({"filepath": filepath, "reason": str(e)})

        # Step 2: Process each DataFrame with appropriate handler
        formatted_dfs = []
        processed_files = set()
        
        for df in loaded_dfs:
            filepath = getattr(df, 'filepath', str(df.attrs.get('filepath', 'unknown')))
            try:
                formatted_df = process_with_handler(df, filepath)
                if formatted_df is not None and not formatted_df.empty:
                    formatted_dfs.append(formatted_df)
                    # Add to processed files if not already added by process_with_handler
                    if filepath not in processed_files:
                        processed_files_tracker.add(filepath)
                        processed_files.add(filepath)
                else:
                    log(f"No handler found for file: {filepath}", level="warning")
                    unrecognized_files.append(
                        {"filepath": filepath, "reason": "No matching handler"}
                    )
            except Exception as e:
                log(f"Error processing file {filepath}: {str(e)}", level="error")
                unrecognized_files.append({"filepath": filepath, "reason": str(e)})

        # Step 3: Merge all formatted DataFrames
        stats = {}
        if formatted_dfs:
            merged_df, stats = merge_dataframes(formatted_dfs)
            # Get processed files from our tracking set
            processed_files_list = list(processed_files)
            stats["processed_files"] = processed_files_list
            log(f"Successfully processed {len(processed_files_list)} files", level="info")
        else:
            merged_df = pd.DataFrame()
            log("No files were successfully processed", level="warning")
            
        processed_files = stats.get("processed_files", [])

        # Handle unrecognized files - only process those that weren't successfully processed
        unrecognized_count = 0
        for unrecognized in unrecognized_files:
            filepath = unrecognized["filepath"]
            reason = unrecognized["reason"]
            
            # Skip if this file was successfully processed
            if filepath in processed_files:
                log(f"Skipping unrecognized file that was actually processed: {filepath}", level="debug")
                continue
                
            log(f"Processing unrecognized file: {filepath} - {reason}", level="warning")
            
            # Add to tracker for global accessibility
            unrecognised_files_tracker.add(filepath)

            # Move file to unrecognized folder
            if move_to_unrecognised(filepath):
                unrecognized_count += 1
                log(f"Moved unrecognized file: {filepath}", level="info")
            else:
                log(f"Failed to move unrecognized file: {filepath}", level="error")

        # Add unrecognized count to stats
        stats["unrecognized_files"] = unrecognized_count
        if unrecognized_count > 0:
            log(f"Moved {unrecognized_count} unrecognized files to unrecognised folder", level="warning")

        # If we have processed files, continue with backup and save
        if not merged_df.empty:
            # Get the list of processed files from the tracker
            processed_files_list = processed_files_tracker.get()

            # Only back up files that were successfully processed
            backup_status, backup_result = back_up_originals(
                processed_files_list, save_folder
            )
            if not backup_status:
                raise FileBackupError(backup_result)

            # Extract backup message and stats
            backup_msg = (
                backup_result["message"]
                if isinstance(backup_result, dict)
                else backup_result
            )
            backup_stats = backup_result if isinstance(backup_result, dict) else {}

            # Handle processed files after backup
            if isinstance(backup_result, dict):
                # Get lists of backed up and skipped files
                backed_up_files = backup_stats.get("backed_up_files", [])
                skipped_files = backup_stats.get("skipped_files", [])

                # Delete original files that have been backed up or skipped
                deleted_count = 0
                for filepath in backed_up_files + skipped_files:
                    try:
                        os.remove(filepath)
                        deleted_count += 1
                    except Exception as e:
                        log(
                            f"Failed to delete original file {filepath}: {str(e)}",
                            level="warning",
                        )

                # Add deletion count to stats
                backup_stats["deleted_count"] = deleted_count
                backup_msg += f", deleted {deleted_count} original files"

            # Validate data structure and standardize formats before saving
            validated_df, validation_success = validate_core_data_structure(merged_df)

            # Only proceed if validation was successful
            if not validation_success:
                log(
                    "Data validation failed - cannot proceed with saving or database update",
                    level="error",
                )
                return {
                    "process_success": False,
                    "error": "Data validation failed - critical columns missing",
                    "backup_info": backup_stats,
                }

            # Save master file first
            output_path = os.path.join(save_folder, "fmMaster.csv")
            save_result = save_master_file(validated_df, output_path)

            # Update database separately if requested
            database_update_info = {}
            if (
                update_database
                and isinstance(save_result, dict)
                and save_result.get("save_success", False)
            ):
                # Update database with the validated DataFrame
                db_result = update_database_records(
                    validated_df, source_file=output_path
                )

                if db_result.get("database_updated", False):
                    database_update_info = {
                        "database_updated": True,
                        "added_count": db_result.get("import_result", {}).get(
                            "added_count", 0
                        ),
                        "duplicate_count": db_result.get("import_result", {}).get(
                            "duplicate_count", 0
                        ),
                        "error_count": db_result.get("import_result", {}).get(
                            "error_count", 0
                        ),
                    }

            if isinstance(save_result, dict) and save_result.get("save_success", False):
                # Update master file info in central data management
                ud_config.update_master_location(output_path)

                # Publish processing completed event
                UpdateDataEventService.publish_processing_completed(
                    {
                        "stats": stats,
                        "processed_files": processed_files,
                        "backup_stats": backup_stats,
                    }
                )

                # Return results
                return {
                    "status": "success",
                    "message": "Processing completed successfully",
                    "details": {
                        "input_files": len(filepaths),
                        "processed_files": len(processed_files),
                        "unrecognized_files": len(unrecognized_files),
                        "duplicates_removed": stats.get("duplicates_removed", 0),
                        "output_rows": len(merged_df),
                        "backup_message": backup_msg,
                        "backup_files_count": backup_stats.get("backed_up_count", 0),
                        "backup_skipped_count": backup_stats.get("skipped_count", 0),
                        "unrecognized_details": unrecognized_files,
                        **database_update_info,
                    },
                    "output_path": output_path,
                }
            else:
                return {
                    "status": "error",
                    "message": "Error saving master file",
                    "details": {
                        "input_files": len(filepaths),
                        "processed_files": len(processed_files),
                        "unrecognized_files": len(unrecognized_files),
                        "unrecognized_details": unrecognized_files,
                    },
                }
        else:
            return {
                "status": "error",
                "message": "No files could be processed",
                "details": {
                    "input_files": len(filepaths),
                    "processed_files": 0,
                    "unrecognized_files": len(unrecognized_files),
                    "unrecognized_details": unrecognized_files,
                },
            }
    except FileBackupError as e:
        return {
            "status": "error",
            "message": "Failed to backup original files",
            "details": {
                "error": str(e),
                "error_details": getattr(e, "details", {}),
                "processed_files": len(processed_files),
                "unrecognized_files": len(unrecognized_files),
                "unrecognized_details": unrecognized_files,
            },
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Unexpected error: {str(e)}",
            "details": {
                "error_type": type(e).__name__,
                "error": str(e),
                "processed_files": len(processed_files),
                "unrecognized_files": len(unrecognized_files),
                "unrecognized_details": unrecognized_files,
            },
        }


# Database functions have been moved to dw_pipeline.py


if __name__ == "__main__":
    pass
