"""
Utility for extracting file display information.
"""

import os
import pprint
from typing import Dict, Any, Optional

import pandas as pd

from ...utils.file_utils import load_csv_to_df
from ...utils.statement_handlers._handler_registry import get_handler


class FileDisplayHelper:
    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """
        Extract file information using format detector
        
        Args:
            file_path: Path to the file
        
        Returns:
            Dictionary containing file information including size and format
        """
        try:
            # Read the file with consistent metadata
            df = load_csv_to_df(file_path)
            if df is None:
                raise ValueError(f"Failed to load file: {file_path}")
                
            # Get file size from attributes if available, otherwise calculate it
            size = getattr(df, 'size_bytes', os.path.getsize(file_path))
            size_str = f"{size / 1024:.1f} KB" if size < 1024 * 1024 else f"{size / (1024 * 1024):.1f} MB"
            
            # Use statement handlers for format detection
            handler = get_handler(df, file_path)
            
            # Prepare format info
            format_info = {
                'bank_type': 'Unknown',
                'format_type': 'Unknown',
                'handler': None
            }
            
            if handler:
                format_info.update({
                    'bank_type': handler.statement_format.bank_name,
                    'format_type': handler.statement_format.variant,
                    'handler': handler.__class__.__name__
                })
            
            # Prepare file info dictionary
            file_info = {
                'path': file_path,
                'size_bytes': size,
                'size_str': size_str,
                **format_info  # Unpack bank_type and format_type
            }
            
            # Pretty print to terminal
            print(f"File Information for {file_path}:")
            pprint.pprint(file_info, width=80, compact=True)
            print("-" * 40)
            
            return file_info
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return {
                'path': file_path,
                'size_bytes': 0,
                'size_str': 'N/A',
                'bank_type': None,
                'format_type': None,
                'handler': None,
                'error': str(e)
            }

    @staticmethod
    @staticmethod
    def process_files(file_paths):
        """
        Process multiple files and collect their information
        
        Args:
            file_paths: List of file paths to process
            
        Returns:
            Dictionary mapping file paths to their processed information
        """
        results = {}
        for file_path in file_paths:
            try:
                results[file_path] = FileDisplayHelper.get_file_info(file_path)
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
                results[file_path] = {
                    'path': file_path,
                    'error': str(e),
                    'size_bytes': 0,
                    'size_str': 'N/A',
                    'bank_type': None,
                    'format_type': None,
                    'handler': None
                }
        return results
