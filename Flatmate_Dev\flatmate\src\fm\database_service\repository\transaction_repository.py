"""
Transaction repository interface.
Defines the contract for transaction data storage.
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any


@dataclass
class ImportResult:
    """Result of an import operation."""
    added_count: int = 0
    duplicate_count: int = 0
    error_count: int = 0
    errors: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []


@dataclass
class Transaction:
    """Transaction data model."""
    date: datetime
    description: str
    amount: float
    account_number: str = ""
    transaction_type: str = ""
    category: str = ""
    notes: str = ""
    tags: str = ""
    source_bank: str = ""
    source_file: str = ""
    import_date: Optional[datetime] = None
    modified_date: Optional[datetime] = None
    transaction_id: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert transaction to dictionary."""
        return {
            "date": self.date.isoformat() if self.date else None,
            "description": self.description,
            "amount": self.amount,
            "account_number": self.account_number,
            "transaction_type": self.transaction_type,
            "category": self.category,
            "notes": self.notes,
            "tags": self.tags,
            "source_bank": self.source_bank,
            "source_file": self.source_file,
            "import_date": self.import_date.isoformat() if self.import_date else None,
            "modified_date": self.modified_date.isoformat() if self.modified_date else None,
            "transaction_id": self.transaction_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Transaction':
        """Create transaction from dictionary."""
        # Handle date parsing - date is required for a valid transaction
        date = data.get("date")
        if date is None:
            # If no date is provided, use current date rather than allowing None
            date = datetime.now()
        elif isinstance(date, str):
            date = datetime.fromisoformat(date)
        # At this point, date should be a valid datetime object
            
        import_date = data.get("import_date")
        if isinstance(import_date, str):
            import_date = datetime.fromisoformat(import_date)
            
        modified_date = data.get("modified_date")
        if isinstance(modified_date, str):
            modified_date = datetime.fromisoformat(modified_date)
            
        return cls(
            date=date,  # Now guaranteed to be a datetime object
            description=data.get("description", ""),
            amount=data.get("amount", 0.0),
            account_number=data.get("account_number", ""),
            transaction_type=data.get("transaction_type", ""),
            category=data.get("category", ""),
            notes=data.get("notes", ""),
            tags=data.get("tags", ""),
            source_bank=data.get("source_bank", ""),
            source_file=data.get("source_file", ""),
            import_date=import_date,
            modified_date=modified_date,
            transaction_id=data.get("transaction_id")
        )


class TransactionRepository(ABC):
    """Interface for transaction data storage."""
    
    @abstractmethod
    def add_transactions(self, transactions: List[Transaction]) -> ImportResult:
        """
        Add new transactions to the repository.
        
        Args:
            transactions: List of transactions to add
            
        Returns:
            ImportResult with counts of added and duplicate transactions
        """
        pass
    
    @abstractmethod
    def get_transactions(self, filters: Optional[Dict] = None) -> List[Transaction]:
        """
        Retrieve transactions matching the filters.
        
        Args:
            filters: Dictionary of filter criteria
            
        Returns:
            List of matching transactions
        """
        pass
    
    @abstractmethod
    def update_transaction(self, transaction_id: Optional[int], data: Dict) -> bool:
        """
        Update a specific transaction.
        
        Args:
            transaction_id: ID of the transaction to update
            data: Dictionary of fields to update
            
        Returns:
            True if update was successful
        """
        pass
    
    @abstractmethod
    def delete_transaction(self, transaction_id: int) -> bool:
        """
        Mark a transaction as deleted.
        
        Args:
            transaction_id: ID of the transaction to delete
            
        Returns:
            True if deletion was successful
        """
        pass
        
    @abstractmethod
    def delete_all_transactions(self) -> int:
        """
        Mark all transactions as deleted.
        
        Returns:
            Number of transactions deleted
        """
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict:
        """
        Get statistics about the stored transactions.
        
        Returns:
            Dictionary with transaction statistics
        """
        pass
        
    @abstractmethod
    def add_transactions_from_df(self, df, source_file: Optional[str] = None) -> ImportResult:
        """
        Add new transactions from a pandas DataFrame.
        
        Args:
            df: Pandas DataFrame containing transaction data
            source_file: Optional source file path for reference
            
        Returns:
            ImportResult with counts of added and duplicate transactions
        """
        pass
