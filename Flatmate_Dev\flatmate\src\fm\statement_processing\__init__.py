"""Statement-processing façade.

Import this module rather than fm.modules.update_data.utils.*. For now it
re-exports the existing update_data pipeline so callers have a stable API.
Later the real code can be moved here with no changes required in callers.
"""

# Reuse low-level helpers from Update-Data pipeline
from fm.modules.update_data.utils.dw_pipeline import (
    load_csv_file as _load_csv_file,
    process_with_handler as _process_with_handler,
    merge_dataframes as _merge_dataframes,
)

__all__ = [
    "process_files",
]

# Public API ---------------------------------------------------------------

import pandas as pd
from typing import List, Union


def process_files(file_paths: Union[str, List[str]]) -> pd.DataFrame:
    """Parse, clean and merge bank-statement files.

    This is a *thin* convenience wrapper around the existing helper
    functions in `update_data.utils.dw_pipeline`.  It deliberately
    avoids backup/DB-update logic handled by ``dw_director`` because the
    Categorise module only needs an in-memory DataFrame.
    """
    if isinstance(file_paths, str):
        file_paths = [file_paths]

    loaded_dfs = []
    for path in file_paths:
        df = _load_csv_file(path)
        if df is None:
            continue
        formatted = _process_with_handler(df, path)
        if formatted is not None and not formatted.empty:
            loaded_dfs.append(formatted)

    merged_df, _stats = _merge_dataframes(loaded_dfs)
    return merged_df
