# Modular Feature Architecture

## Architectural Philosophy
Maintain the existing module-based structure while introducing flexible configuration and feature management.

## Current Structure Preservation
```
flatmate/
└── src/
    └── flatmate/
        ├── modules/
        │   ├── update_data/
        │   │   ├── __init__.py
        │   │   ├── ud_job_manager.py
        │   │   ├── ud_presenter.py
        │   │   ├── ud_view.py
        │   │   ├── config.py           # New: Module-specific configuration
        │   │   └── feature_flags.py    # New: Module-specific feature management
        │   └── other_modules/
        └── utils/
            └── config_manager.py       # Global configuration helpers
```

## Module-Level Configuration Pattern

### Module Configuration (Example)
```python
# modules/update_data/config.py
from dataclasses import dataclass
from typing import Optional

@dataclass
class UpdateDataConfig:
    """Configuration specific to Update Data module"""
    max_job_sheets: int = 10
    default_source_type: str = 'bank_statement'
    enable_advanced_parsing: bool = False

class UpdateDataConfigManager:
    def __init__(self, global_config_manager):
        self._global_config = global_config_manager
        self._module_config = self._load_module_config()
    
    def _load_module_config(self) -> UpdateDataConfig:
        """
        Load configuration with fallback to global settings
        
        Priority:
        1. Module-specific TOML config
        2. Global configuration
        3. Default values
        """
        # Load from module-specific TOML
        module_config_path = self._find_module_config()
        if module_config_path:
            return self._parse_module_config(module_config_path)
        
        # Fallback to global config
        return UpdateDataConfig(
            max_job_sheets=self._global_config.get('update_data.max_job_sheets', 10),
            default_source_type=self._global_config.get('update_data.default_source_type', 'bank_statement')
        )
    
    def get(self, key: str, default: Optional[Any] = None):
        """Retrieve a specific configuration value"""
        return getattr(self._module_config, key, default)
```

## Feature Flag Implementation
```python
# modules/update_data/feature_flags.py
class UpdateDataFeatureFlags:
    def __init__(self, config_manager):
        self._config = config_manager
    
    def is_advanced_parsing_enabled(self) -> bool:
        """Check if advanced parsing is enabled"""
        return self._config.get('enable_advanced_parsing', False)
    
    def get_max_job_sheets(self) -> int:
        """Retrieve maximum number of job sheets"""
        return self._config.get('max_job_sheets', 10)

# Usage in module
class UpdateDataJobManager:
    def __init__(self, feature_flags: UpdateDataFeatureFlags):
        self._feature_flags = feature_flags
        self._max_job_sheets = feature_flags.get_max_job_sheets()
        
        if feature_flags.is_advanced_parsing_enabled():
            # Use advanced parsing logic
            self._use_advanced_parsing()
```

## Global Configuration Manager
```python
# utils/config_manager.py
import os
import tomli
from typing import Any, Dict

class GlobalConfigManager:
    def __init__(self, config_dir: str = 'config'):
        self._config: Dict[str, Any] = self._load_configs(config_dir)
    
    def _load_configs(self, config_dir: str) -> Dict[str, Any]:
        """
        Load configurations from multiple sources
        
        Precedence:
        1. Environment-specific config
        2. Base configuration
        3. Default values
        """
        base_config_path = os.path.join(config_dir, 'base.toml')
        env_config_path = os.path.join(config_dir, f'{os.getenv("FLATMATE_ENV", "development")}.toml')
        
        config = {}
        
        # Load base configuration
        if os.path.exists(base_config_path):
            with open(base_config_path, 'rb') as f:
                config.update(tomli.load(f))
        
        # Overlay environment-specific config
        if os.path.exists(env_config_path):
            with open(env_config_path, 'rb') as f:
                config.update(tomli.load(f))
        
        return config
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Retrieve a configuration value
        
        Supports dot notation for nested keys
        """
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict):
                value = value.get(k, {})
            else:
                return default
        
        return value if value != {} else default
```

## Configuration File Example
```toml
# config/base.toml
[app]
name = "Flatmate"
version = "1.0.0"

[update_data]
max_job_sheets = 10
default_source_type = "bank_statement"
enable_advanced_parsing = false

# config/development.toml
[update_data]
max_job_sheets = 20
enable_advanced_parsing = true
```

## Key Principles
1. **Module-Specific Configuration**: Each module manages its own configuration
2. **Flexible Hierarchy**: Global to module-specific configuration
3. **Feature Flag Support**: Easy feature toggling
4. **Minimal Restructuring**: Maintains existing module structure

## Benefits
- No major refactoring required
- Clear configuration management
- Easy to extend and modify
- Supports different environments
- Keeps modules independent

## Recommended Next Steps
1. Implement base configuration classes
2. Create module-specific configuration managers
3. Integrate feature flags
4. Add environment-specific configurations
