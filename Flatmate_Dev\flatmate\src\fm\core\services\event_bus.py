from typing import Any, Callable, Dict, List
import threading

class EventBus:
    """
    Thread-safe event bus for inter-module communication
    
    Supports:
    - Multiple listeners per event
    - Synchronous event publishing
    - Type hinting
    - Flexible event handling
    """
    
    def __init__(self):
        # Dictionary to store event listeners
        self._listeners: Dict[str, List[Callable]] = {}
        # Thread lock for thread-safe operations
        self._lock = threading.Lock()
    
    def subscribe(self, event_type: str, listener: Callable):
        """
        Register a listener for a specific event type
        
        Args:
            event_type: A string identifying the event
            listener: A callable that will be invoked when the event occurs
        """
        with self._lock:
            if event_type not in self._listeners:
                self._listeners[event_type] = []
            self._listeners[event_type].append(listener)
    
    def unsubscribe(self, event_type: str, listener: Callable):
        """
        Remove a specific listener for an event type
        
        Args:
            event_type: The event type to unsubscribe from
            listener: The specific listener to remove
        """
        with self._lock:
            if event_type in self._listeners:
                self._listeners[event_type].remove(listener)
    
    def publish(self, event_type: str, data: Any = None):
        """
        Broadcast an event to all registered listeners
        
        Args:
            event_type: The type of event to broadcast
            data: Optional data to pass to listeners
        """
        # Create a copy of listeners to avoid modification during iteration
        listeners = self._listeners.get(event_type, []).copy()
        
        for listener in listeners:
            try:
                listener(data)
            except Exception as e:
                # Log or handle listener exceptions
                print(f"Error in event listener for {event_type}: {e}")

# Global event bus instance
global_event_bus = EventBus()

class Events:
    """
    Centralized event type definitions
    
    Provides a single source of truth for event types
    Helps prevent typos and provides autocomplete
    """
    # System-wide events
    APP_STARTED = 'app_started'
    APP_SHUTDOWN = 'app_shutdown'
    
    # Module-specific events
    MODULE_INITIALIZED = 'module_initialized'
    MODULE_ERROR = 'module_error'
    
    # Data-related events
    DATA_PROCESSING_STARTED = 'data_processing_started'
    DATA_PROCESSING_COMPLETE = 'data_processing_complete'
    
    # Configuration events
    CONFIG_UPDATED = 'config_updated'
    
    # Logging events
    LOG_EVENT = 'log_event'
    VIEW_CHANGE_REQUESTED = 'view_change_requested'
    
    # InfoBar events
    INFO_MESSAGE = 'info_message'
    INFO_CLEAR = 'info_clear'
