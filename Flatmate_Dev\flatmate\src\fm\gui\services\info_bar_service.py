"""
Service for managing info bar messages across the application.
"""

from PySide6.QtCore import QObject

from ...core.services.event_bus import Events, global_event_bus


class InfoBarService(QObject):
    """Service for managing info bar messages across the application."""

    # Singleton instance
    _instance = None

    def __init__(self):
        """Initialize the info bar service."""
        super().__init__()
        self.event_bus = global_event_bus

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of the service.

        Returns:
            InfoBarService: The singleton instance
        """
        if cls._instance is None:
            cls._instance = InfoBarService()
        return cls._instance

    def publish_message(self, message, priority=0):
        """Publish a message to the info bar.

        Args:
            message: The message to display
            priority: Message priority (higher = more important)
        """
        # Publish the message using the event bus
        if message:
            self.event_bus.publish(Events.INFO_MESSAGE, message)

    def show(self):
        """Request to show the info bar."""
        # No longer needed as visibility is handled by the InfoBar itself
        pass

    def hide(self):
        """Request to hide the info bar."""
        # No longer needed as visibility is handled by the InfoBar itself
        pass

    def clear(self):
        """Clear the current message."""
        self.event_bus.publish(Events.INFO_CLEAR, None)
