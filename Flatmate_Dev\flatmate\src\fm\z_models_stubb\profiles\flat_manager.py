from typing import Dict, Any
from .person import PersonProfile

class FlatManagerProfile(PersonProfile):
    """Profile for flat/property managers with additional management-specific attributes."""
    
    def __init__(self, profile_id: str = None):
        super().__init__(profile_id)
        self.managed_properties: list[str] = []  # List of property IDs managed by this manager
        self.management_company: str = ""
        self.license_number: str = ""
        self.management_start_date: str = ""
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the manager profile to a dictionary."""
        data = super().to_dict()
        data.update({
            'managed_properties': self.managed_properties,
            'management_company': self.management_company,
            'license_number': self.license_number,
            'management_start_date': self.management_start_date
        })
        return data
    
    def from_dict(self, data: Dict[str, Any]) -> None:
        """Load manager profile data from a dictionary."""
        super().from_dict(data)
        self.managed_properties = data.get('managed_properties', [])
        self.management_company = data.get('management_company', '')
        self.license_number = data.get('license_number', '')
        self.management_start_date = data.get('management_start_date', '')
