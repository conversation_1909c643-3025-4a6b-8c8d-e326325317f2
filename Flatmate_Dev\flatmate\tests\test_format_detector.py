import sys
import os
import re
import pytest
import pandas as pd

# Add the source directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(
    os.path.dirname(__file__), 
    '..', 
    'src'
)))

from flatmate.modules.update_data.utils.formatting.dw_format_detector import FormatDetector
from flatmate.modules.update_data.utils.formatting.formats.kiwibank import SIMPLE_CSV, FULL_CSV

def print_csv_columns(df: pd.DataFrame, filepath: str):
    """Print column names from a DataFrame."""
    print(f"\nColumns in {os.path.basename(filepath)}:")
    for i, col in enumerate(df.columns):
        print(f"{i}: {col}")
    print()

def test_format_detection():
    # Directory containing test CSV files
    test_data_dir = os.path.join(os.path.dirname(__file__), 'test_data')
    
    # Find all CSV files
    csv_files = [
        f for f in os.listdir(test_data_dir)
        if f.lower().endswith('.csv') or f.lower().endswith('.csv')
    ]
    
    assert len(csv_files) > 0, "No CSV files found in test directory"
    
    # Track which files were successfully processed and which were not
    processed_files = []
    unrecognized_files = []
    
    for csv_file in csv_files:
        csv_path = os.path.join(test_data_dir, csv_file)
        print(f"\n{'='*50}")
        print(f"Testing {csv_file}")
        print(f"{'='*50}")
        
        # Read the CSV file
        df = pd.read_csv(csv_path)
        
        # Print column information
        print(f"Total Columns: {len(df.columns)}")
        print(f"Columns: {df.columns.tolist()}")
        
        # Create a FormatDetector instance
        detector = FormatDetector()
        
        # Detect the format
        result = detector.get_source_file_type(df)
        
        print("\n--- Detection Result ---")
        print(f"Bank Type: {result['bank_type']}")
        print(f"Format Type: {result['format_type']}")
        
        # Validate formats if a format was detected
        if result['bank_type'] is not None:
            try:
                if result['format_type'] == 'simple':
                    simple_result = detector.validate_simple_format(df)
                    print(f"\nSimple Format Validation: {'PASSED' if simple_result else 'FAILED'}")
                    
                    # If simple format fails, print diagnostic information
                    if not simple_result:
                        first_col = str(df.columns[0])
                        print(f"First Column: {first_col}")
                        
                        # Check unnamed columns
                        unnamed_check = all(str(col).startswith("Unnamed:") for col in df.columns[1:5])
                        print(f"First 4 Columns Unnamed: {unnamed_check}")
                
                elif result['format_type'] == 'full':
                    full_result = detector.validate_full_format(df)
                    print(f"\nFull Format Validation: {'PASSED' if full_result else 'FAILED'}")
                    
                    # If full format fails, print diagnostic information
                    if not full_result:
                        required_cols = {"Account number", "Date", "Balance"}
                        df_cols = {str(col) for col in df.columns}
                        missing_cols = required_cols - df_cols
                        print(f"Missing Required Columns: {missing_cols}")
                
                processed_files.append(csv_file)
            except Exception as e:
                print(f"Validation error: {e}")
        else:
            # File was not recognized
            unrecognized_files.append({
                'filename': csv_file,
                'columns': df.columns.tolist(),
                'column_count': len(df.columns)
            })
    
    # Print out unrecognized files
    if unrecognized_files:
        print("\n--- Unrecognized Files ---")
        for file_info in unrecognized_files:
            print(f"File: {file_info['filename']}")
            print(f"  Columns: {file_info['columns']}")
            print(f"  Column Count: {file_info['column_count']}")
    
    # Ensure at least one file was successfully processed
    assert len(processed_files) > 0, "No files could be processed"
    
    print("\nSuccessfully processed files:")
    for file in processed_files:
        print(f"- {file}")

if __name__ == "__main__":
    pytest.main([__file__])
