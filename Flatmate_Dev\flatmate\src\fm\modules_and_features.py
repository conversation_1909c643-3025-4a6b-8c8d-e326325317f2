"""
Module and Feature Mapping

This module provides a comprehensive map of all modules and features in the FlatMate application.
It serves as both a functional tool for module management and a visual guide to the project's current state.
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Set
from os import path

# ===== MODULE STATUS =====
class ModuleStatus(Enum):
    """Status of a module in the development lifecycle."""
    ACTIVE = "active"        # Fully implemented and working
    IN_DEVELOPMENT = "dev"   # Partially implemented
    PLANNED = "planned"      # Planned but not implemented
    DEPRECATED = "deprecated" # No longer in use

# ===== MODULE DEFINITIONS =====
class Module(Enum):
    """All application modules, regardless of status."""
    # Active modules
    HOME = "home"
    UPDATE_DATA = "update_data"
    
    # In development modules
    VIEW_DATA = "view_data"
    PROFILES = "profile"
    
    # Planned modules
    REPORTS = "reports"
    CATEGORIZE = "categorize"

# ===== FEATURE DEFINITIONS =====
class Feature(Enum):
    """Features available across different modules."""
    # Core features
    SETTINGS_PANEL = "settings_panel"
    DEV_SETTINGS = "dev_settings_pane"
    
    # Home features
    DASHBOARD = "dashboard"
    QUICK_LINKS = "quick_links"
    
    # Update data features
    CSV_IMPORT = "csv_import"
    DATA_ENTRY = "data_entry"
    
    # View data features
    DATA_TABLE = "data_table"
    DATA_FILTER = "data_filter"
    
    # Profile features
    USER_PROFILE = "user_profile"
    FLATMATE_PROFILE = "flatmate_profile"

# ===== UI ELEMENTS =====
class NavPaneItem(Enum):
    """Navigation pane items that can be clicked."""
    HOME = "home"
    IMPORT_DATA = "import_data"  # Maps to UPDATE_DATA module
    VIEW_DATA = "view_data"
    PROFILES = "profile"
    SETTINGS = "settings"

# ===== RELATIONSHIPS =====
@dataclass
class ModuleInfo:
    """Comprehensive information about a module."""
    module: Module
    status: ModuleStatus
    display_name: str
    description: str
    features: Set[Feature]
    nav_items: Set[NavPaneItem]
    path: str  # Relative path from modules/

# ===== MODULE MAP =====
MODULE_MAP: Dict[Module, ModuleInfo] = {
    Module.HOME: ModuleInfo(
        module=Module.HOME,
        status=ModuleStatus.ACTIVE,
        display_name="Home Dashboard",
        description="Main dashboard and application entry point",
        features={Feature.DASHBOARD, Feature.QUICK_LINKS},
        nav_items={NavPaneItem.HOME},
        path="home"
    ),
    Module.UPDATE_DATA: ModuleInfo(
        module=Module.UPDATE_DATA,
        status=ModuleStatus.ACTIVE,
        display_name="Update Data",
        description="Import and update data from various sources",
        features={Feature.CSV_IMPORT, Feature.DATA_ENTRY},
        nav_items={NavPaneItem.IMPORT_DATA},
        path="update_data"
    ),
    Module.VIEW_DATA: ModuleInfo(
        module=Module.VIEW_DATA,
        status=ModuleStatus.IN_DEVELOPMENT,
        display_name="View Data",
        description="View and filter transaction data",
        features={Feature.DATA_TABLE, Feature.DATA_FILTER},
        nav_items={NavPaneItem.VIEW_DATA},
        path="view_data"
    ),
    Module.PROFILES: ModuleInfo(
        module=Module.PROFILES,
        status=ModuleStatus.IN_DEVELOPMENT,
        display_name="Profiles",
        description="Manage user and flatmate profiles",
        features={Feature.USER_PROFILE, Feature.FLATMATE_PROFILE},
        nav_items={NavPaneItem.PROFILES},
        path="profile"
    ),
}

# ===== HELPER FUNCTIONS =====
def get_module_path(module_name: str) -> Optional[str]:
    """Get the path to a module directory."""
    for module, info in MODULE_MAP.items():
        if module.value == module_name:
            return path.join(path.dirname(__file__), "modules", info.path)
    return None

def get_active_modules() -> List[Module]:
    """Get a list of all active modules."""
    return [m for m, info in MODULE_MAP.items() 
            if info.status == ModuleStatus.ACTIVE]

def get_modules_by_status(status: ModuleStatus) -> List[Module]:
    """Get all modules with a specific status."""
    return [m for m, info in MODULE_MAP.items() 
            if info.status == status]

def get_module_by_nav_item(nav_item: str) -> Optional[Module]:
    """Get the module associated with a navigation item."""
    for module, info in MODULE_MAP.items():
        if any(item.value == nav_item for item in info.nav_items):
            return module
    return None

def get_features_by_module(module_name: str) -> List[Feature]:
    """Get all features associated with a module."""
    for module, info in MODULE_MAP.items():
        if module.value == module_name:
            return list(info.features)
    return []
