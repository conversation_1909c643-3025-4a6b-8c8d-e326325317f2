import os
from typing import TYPE_CHECKING, Optional

import pandas as pd

if TYPE_CHECKING:
    from ._base_statement_handler import StatementHandler
else:
    # Import for runtime
    from ._base_statement_handler import StatementHandler

from .asb_standard_csv_handler import <PERSON>b<PERSON><PERSON>dardCSVHandler
from .coop_standard_csv_handler import CoopStandardCSVHandler
from .kiwibank_basic_csv_handler import KiwibankBasicCSVHandler
from .kiwibank_full_csv_handler import KiwibankFullCSVHandler

# from .test_csv_handler import TestCSVHandler

# List of handler classes for registration
STATEMENT_HANDLERS = [
    # Put TestCSVHandler first so it's checked before other handlers
    <PERSON>wi<PERSON>Basic<PERSON><PERSON><PERSON><PERSON><PERSON>,
    KiwibankFullCSVHandler,
    CoopStandardCSVHandler,
    AsbStandardCSVHandler,
]

# Create all handlers at module level for singleton usage
_handlers = [handler() for handler in STATEMENT_HANDLERS]


def get_handler(df: pd.DataFrame, filepath: str) -> Optional["StatementHandler"]:
    """Get appropriate handler for DataFrame format"""
    # Import logging here to avoid circular imports
    from fm.core.services.logger import Logger

    logger = Logger()

    filename = os.path.basename(filepath)
    logger.debug(f"[handler_registry] Trying to find handler for file: {filename}")
    logger.debug(f"[handler_registry] DataFrame shape: {df.shape}")
    logger.debug(f"[handler_registry] Column names: {list(df.columns)}")

    for handler in _handlers:
        handler_name = handler.__class__.__name__
        logger.debug(f"[handler_registry] Trying handler: {handler_name}")
        if handler.matches_statement_type(df):
            logger.debug(f"[handler_registry] Found matching handler: {handler_name}")
            return handler
        logger.debug(f"[handler_registry] Handler {handler_name} did not match")

    logger.debug(f"[handler_registry] No matching handler found for file: {filename}")
    return None
