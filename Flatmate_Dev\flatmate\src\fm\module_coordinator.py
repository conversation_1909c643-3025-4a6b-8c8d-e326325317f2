"""
Module coordinator for the FlatMate application.
Manages module initialization, transitions, and lifecycle.
"""

from typing import Dict, Any, Optional, Callable
from functools import partial
from .core.config import config  # Add config import
from .core.config.keys import ConfigKeys  # Add config keys import
from .core.services.logger import Logger
from .modules.home.home_presenter import HomePresenter
from .modules.update_data.ud_presenter import UpdateDataPresenter
from .modules.categorize.cat_presenter import CategorizePresenter

# Create module logger
logger = Logger()


class ModuleCoordinator:
    """Coordinates all modules in the application."""
    
    # Mapping from navigation button IDs to module IDs
    # This is the reverse of NavPane.MODULE_TO_NAV_MAP
    NAV_TO_MODULE_MAP = {
        'import_data': 'update_data',  # Nav button is 'import_data' but module is 'update_data'
    }
    
    def __init__(self, main_window):
        """Initialize the coordinator.
        
        Args:
            main_window: The main window instance that serves as a vessel
        """
        logger.info("Initializing Module Coordinator")
        self.main_window = main_window
        self.current_module = None
        self.module_factories = {}
        
        # Load module preferences using new config
        self.recent_modules = config.get_value(ConfigKeys.Window.RECENT_FILES, default=[])
        logger.debug(f"Loaded recent modules: {self.recent_modules}")
        
    def initialize_modules(self):
        """Initialize module factories and configure transitions."""
        logger.info("Initializing Module Factories")
        
        # Register module factories
        self.module_factories = {
            'home': lambda: HomePresenter(self.main_window),
            'update_data': lambda: UpdateDataPresenter(self.main_window),
            'categorize': lambda: CategorizePresenter(self.main_window)
        }
        logger.debug(f"Registered module factories: {list(self.module_factories.keys())}")
    
    def start(self):
        """Start the application with the home module."""
        logger.info("Starting Application")
        self.transition_to('home')
    
    def _connect_module_transitions(self, module):
        """Connect a module's request_transition to our transition_to method.
        
        Args:
            module: The module instance to connect
        """
        if hasattr(module, 'request_transition'):
            # Create a bound method for this specific module's transitions
            module.request_transition = partial(self.transition_to)
            print(f"Connected transitions for {type(module).__name__}")
    
    def transition_to(self, module_name: str, **params: Dict[str, Any]):
        """Transition to a new module.
        
        Args:
            module_name: Name of the module to transition to
            **params: Optional parameters to pass to the module
        """
        # Map navigation button ID to module ID if needed
        actual_module_name = self.NAV_TO_MODULE_MAP.get(module_name, module_name)
        print(f"\n=== [ModuleCoordinator] Received transition request to '{module_name}' (mapped to '{actual_module_name}') ===")
        
        # Use the mapped module name for the rest of the method
        module_name = actual_module_name
        
        # Cleanup current module if it exists
        if self.current_module:
            print(f"Cleaning up {type(self.current_module).__name__}")
            
            # Call cleanup on module if available
            if hasattr(self.current_module, 'cleanup'):
                self.current_module.cleanup()
            
            self.current_module = None
        
        # Create and initialize new module
        if module_name in self.module_factories:
            print(f"Creating {module_name} module")
            self.current_module = self.module_factories[module_name]()
            
            # Connect transitions before initialization
            self._connect_module_transitions(self.current_module)
            
            # Initialize the module
            if hasattr(self.current_module, 'initialize'):
                self.current_module.initialize(**params)
                
            # Update NavPane to reflect current module (visual only)
            # Only if the module has a corresponding navigation button
            if hasattr(self.main_window, 'nav_pane'):
                # highlight_item now returns False if the item doesn't exist
                # This is fine - not all modules need nav buttons
                self.main_window.nav_pane.highlight_item(module_name)
                
            # Save to recent modules using new config
            if module_name not in self.recent_modules:
                self.recent_modules.append(module_name)
                config.set_value(ConfigKeys.Window.RECENT_FILES, self.recent_modules)
        else:
            print(f"Warning: No factory found for module {module_name}")
            # Prevent infinite recursion
            if module_name != 'home':
                self.transition_to('home')  # Fallback to home
            else:
                print("Error: Home module factory not found!")
    
    def get_current_module(self) -> Optional[Any]:
        """Get the currently active module."""
        return self.current_module
