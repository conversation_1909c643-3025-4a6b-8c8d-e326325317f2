# Flatmate App Documentation

## Overview

This directory contains the central documentation for the Flatmate App. It serves as a comprehensive reference point for understanding the system architecture, components, and design principles.

## Documentation Structure

- **SYSTEM_OVERVIEW.md** - Comprehensive overview of the entire system architecture, components, and design principles
- **DOCUMENTATION_DIRECTORY.md** - Complete directory of all documentation with descriptions and links to both central and module-level documentation

### Subdirectories

- **architecture/** - System architecture documentation
- **ui_system/** - UI system documentation
- **modules/** - Central module documentation (module-specific READMEs remain in their respective module directories)
- **development/** - Development guides and standards
- **archive/** - Archived documentation that has been superseded

## Documentation Principles

The documentation follows these key principles:

1. **Clean Architectural Boundaries** - Documentation respects the same clean boundaries as the code, with clear interfaces between components
2. **Central Repository with Distributed READMEs** - This central repository links to module-level READMEs that remain in their respective directories
3. **Explicit Documentation** - Documentation is clear, explicit, and maintainable rather than brief
4. **Pub/Sub Terminology** - "Publish" and "subscribe" are used consistently when referring to the event system

## Using This Documentation

- **New Developers**: Start with the [SYSTEM_OVERVIEW.md](/flatmate/docs/SYSTEM_OVERVIEW.md) to understand the overall architecture
- **Finding Specific Documentation**: Use the [DOCUMENTATION_DIRECTORY.md](/flatmate/docs/DOCUMENTATION_DIRECTORY.md) to locate specific documentation
- **Module-Level Details**: Refer to the README files in specific module directories for implementation details

## Maintaining Documentation

When adding or updating documentation:

1. If it's module-specific, keep it in the relevant module folder
2. If it's system-wide, add it to the appropriate subdirectory here
3. Update the DOCUMENTATION_DIRECTORY.md with a link and description
4. If it supersedes existing documentation, move the old document to the archive folder
5. Keep the SYSTEM_OVERVIEW.md updated with any architectural changes
