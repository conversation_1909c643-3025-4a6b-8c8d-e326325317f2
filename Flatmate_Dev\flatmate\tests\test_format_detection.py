#!/usr/bin/env python3

import os
import sys
import pytest
import pandas as pd
from pathlib import Path
import re

# Add the src directory to Python path
src_path = Path(__file__).parent.parent.parent / "src"
sys.path.append(str(src_path))

from flatmate.modules.update_data.utils.formatting.dw_format_detector import validate_simple_format, validate_full_format
from flatmate.modules.update_data.utils.formatting.formats import kiwibank

def test_format_detection():
    test_dir = Path(__file__).parent
    csv_files = list(test_dir.glob("*.CSV"))
    
    assert len(csv_files) > 0, "No CSV files found in test directory"
    
    for filepath in csv_files:
        print(f"\n{'='*50}")
        print(f"Testing {filepath.name}")
        print(f"{'='*50}")
        
        df = pd.read_csv(filepath)
        print(f"Total Columns: {len(df.columns)}")
        print(f"Columns: {df.columns.tolist()}")
        
        # Try simple format first
        simple_result = False
        try:
            simple_result = validate_simple_format(df, kiwibank.SIMPLE_FORMAT)
            print(f"\nSimple Format Validation: {'PASSED' if simple_result else 'FAILED'}")
            
            # If simple format fails, print why
            if not simple_result:
                first_col = str(df.columns[0])
                account_pattern = kiwibank.SIMPLE_FORMAT["identifiers"]["account_number"]["pattern"]
                print(f"First Column: {first_col}")
                print(f"Account Pattern Match: {bool(re.match(account_pattern, first_col))}")
                
                # Check unnamed columns
                unnamed_check = all(str(col).startswith("Unnamed:") for col in df.columns[1:5])
                print(f"First 4 Columns Unnamed: {unnamed_check}")
        except Exception as e:
            print(f"Simple format validation error: {e}")
        
        # Then try full format
        full_result = False
        try:
            full_result = validate_full_format(df, kiwibank.FULL_FORMAT)
            print(f"\nFull Format Validation: {'PASSED' if full_result else 'FAILED'}")
            
            # If full format fails, print why
            if not full_result:
                required_cols = {"Account number", "Date", "Balance"}
                df_cols = {str(col) for col in df.columns}
                missing_cols = required_cols - df_cols
                print(f"Missing Required Columns: {missing_cols}")
        except Exception as e:
            print(f"Full format validation error: {e}")
        
        # At least one format should pass
        assert simple_result or full_result, f"No valid format found for {filepath.name}"

if __name__ == "__main__":
    pytest.main([__file__])
