# UI Styling Improvements

## Date: 2025-03-24

## QSS Styling Improvements for Consistent Label Appearance

### Problem

Labels in the update_data module were displaying with incorrect colors (black instead of white) due to limitations in Qt's handling of CSS variables in QSS files. This created inconsistent styling across the application, particularly in dark mode.

### Solution

Implemented a consistent naming convention for widget styling that avoids reliance on CSS variables:

1. Created explicit widget object names following the pattern:
   - `[widget_type]_[context]_[purpose]`
   - Example: `lbl_panel_subheading` for subheading labels in panels

2. Updated the style.qss file to use direct color values instead of CSS variables:
   ```css
   QLabel#lbl_panel_subheading,
   QLabel#subheading {  /* Keep old name for backward compatibility */
       color: #CCCCCC;         /* Explicit calm-white color */
       font-size: 1.3em;
       font-weight: bold;
       margin-top: 15px;
   }
   ```

3. Modified all subheading labels in the update_data module to use the new naming convention:
   ```python
   self.source_label.setObjectName("lbl_panel_subheading")
   self.save_label.setObjectName("lbl_panel_subheading")
   self.process_label.setObjectName("lbl_panel_subheading")
   ```

### Benefits

- **Consistent Styling**: Labels now display with the correct colors in all contexts
- **Self-Documenting**: The naming convention clearly indicates the widget type, context, and purpose
- **Maintainable**: Avoids reliance on CSS variables that Qt doesn't handle well
- **Backward Compatible**: Maintains support for existing code using the old naming convention

### Files Modified

#### [style.qss](/flatmate/src/fm/gui/styles/style.qss)
- **Lines modified**: Around line 194-204
- **Changes**: Updated styling rules to use explicit colors instead of CSS variables
- **Before**:
  ```css
  QLabel#subheading {
      color: var(--color-calm-white);
      font-size: 1.3em;
      font-weight: bold;
      margin-top: 15px;
  }
  ```
- **After**:
  ```css
  QLabel#lbl_panel_subheading,
  QLabel#subheading {  /* Keep old name for backward compatibility */
      color: #CCCCCC;         /* Explicit calm-white color */
      font-size: 1.3em;
      font-weight: bold;
      margin-top: 15px;
  }
  ```

#### [widgets.py](/flatmate/src/fm/modules/update_data/_view/left_panel/widgets/widgets.py)
- **Lines modified**: 63-64, 80-81, 104-105
- **Changes**: Updated label object names from "subheading" to "lbl_panel_subheading"
- **Before (source label)**:
  ```python
  self.source_label = QLabel("1. Source Files")
  self.source_label.setObjectName("subheading")
  ```
- **After (source label)**:
  ```python
  self.source_label = QLabel("1. Source Files")
  self.source_label.setObjectName("lbl_panel_subheading")
  ```
- Similar changes were made to the save label and process label

### Future Improvements

This approach should be extended to other parts of the application for consistency:

1. Standardize naming conventions for all UI elements
2. Create a style guide documenting the naming patterns
3. Consider creating helper functions or classes for commonly used widgets with standard styling

### Related Tasks

See the pending tasks document (`/flatmate/src/fm/docs/_pending_tasks.md`) for related styling improvements that need to be addressed.
