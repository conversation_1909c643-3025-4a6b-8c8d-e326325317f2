#!/bin/bash
# gdbo - Get Database Output
# Quick alias for exporting all transactions from the Flatmate database to CSV
# Also supports deleting all transactions with --delete_all flag

# Change to the flatmate directory
cd "$(dirname "$0")"

# Check for delete_all flag
if [ "$1" == "--delete_all" ]; then
    # Ask for confirmation before deleting all transactions
    read -p "Are you sure you want to delete ALL transactions from the database? This cannot be undone. (y/n): " confirm
    if [ "$confirm" == "y" ] || [ "$confirm" == "Y" ]; then
        python cli_db_query.py delete_all
        echo "All transactions have been deleted from the database."
    else
        echo "Operation cancelled."
    fi
else
    # Run the export command and capture the output
    output=$(python cli_db_query.py export)
    
    # Check if there was any output
    if [ -z "$output" ]; then
        echo "No transactions found to export."
    else
        echo "$output"
    fi
fi
