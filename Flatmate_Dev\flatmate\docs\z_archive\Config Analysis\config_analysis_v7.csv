Category,Config Type,Module,Parameter,Type,Default,Required,Description,Source File,Restart Required
env,init,app,env.mode,str,development,yes,Environment mode (dev/prod),config/env.py,yes
env,init,app,env.debug,bool,FALSE,no,Show debug information,config/env.py,yes
env,init,app,env.os_type,str,auto-detect,yes,Operating system (windows/mac/linux),config/env.py,yes
sys,init,app,paths.project_root,Path,.,yes,Project root directory,core/config/paths.py,yes
sys,init,app,paths.logs,Path,PROJECT_ROOT/logs,yes,Log files directory,core/config/paths.py,yes
sys,init,app,paths.config,Path,PROJECT_ROOT/configs,yes,Config files directory,core/config/paths.py,yes
sys,init,app,paths.resources,Path,PROJECT_ROOT/resources,yes,Resources directory,core/config/paths.py,yes
log,runtime,app,log.show_info,bool,TRUE,no,Show info messages,core/logger.py,no
log,runtime,app,log.show_warnings,bool,TRUE,no,Show warning messages,core/logger.py,no
sys,init,update_data,paths.master,Path,~/flatmate/master,yes,Master CSV location,modules/update_data/config/ud_config.py,yes
sys,init,update_data,paths.backup,Path,~/flatmate/backup,yes,Backup directory,modules/update_data/config/ud_config.py,yes
const,init,update_data,formats.date_format,str,%d/%m/%Y,yes,Bank date format,modules/update_data/utils/formatting/formats/bank_format.py,yes
ui,runtime,app,window.left_panel_width,int,250,no,Left panel width,gui/main_window.py,no
ui,runtime,app,window.right_panel_width,int,250,no,Right panel width,gui/main_window.py,no
ui,runtime,app,window.column_widths,dict,{},no,Table column widths by table ID,gui/main_window.py,no
const,init,app,theme.colors.primary,str,#3B8A45,yes,Primary theme color,gui/styles/constants.py,yes
const,init,app,theme.colors.background,str,#2C2C2C,yes,Background color,gui/styles/constants.py,yes
const,init,app,theme.font.size,int,12,yes,Base font size (requires app restart),gui/styles/constants.py,yes