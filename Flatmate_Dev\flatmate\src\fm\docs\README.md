# Flatmate App Documentation

## Overview

This directory contains the central documentation for the Flatmate App. The documentation is organized to maintain clean architectural boundaries while providing comprehensive information about the system.

## Documentation Structure

- **SYSTEM_OVERVIEW.md** - Comprehensive overview of the entire system architecture, components, and design principles
- **DOCUMENTATION_DIRECTORY.md** - Complete directory of all documentation with descriptions and links

### Subdirectories

- **architecture/** - System architecture documentation
- **modules/** - Module-specific documentation
- **ui_system/** - UI system documentation
- **development/** - Development guides and standards
- **_ai_update/** - AI-generated documentation updates
- **archive/** - Archived documentation that has been superseded

## Documentation Principles

1. **Component-Specific Documentation** - READMEs should be kept in the folders where they are relevant
2. **Central System Documentation** - System-level documentation is maintained in this directory
3. **Documentation Directory** - All documentation is indexed in DOCUMENTATION_DIRECTORY.md
4. **Archive Outdated Documentation** - Superseded documentation is archived rather than deleted

## Maintaining Documentation

When adding or updating documentation:

1. If it's component-specific, keep it in the relevant component folder
2. If it's system-wide, add it to the appropriate subdirectory here
3. Update the DOCUMENTATION_DIRECTORY.md with a link and description
4. If it supersedes existing documentation, move the old document to the archive folder
5. Keep the SYSTEM_OVERVIEW.md updated with any architectural changes

## Documentation Format

All documentation should follow these guidelines:

1. Use clear, concise language
2. Include an overview section at the beginning
3. Use proper Markdown formatting
4. Include links to related documentation
5. Focus on being explicit rather than brief
