Module,Function,Parameter,Type,Default,Required,Description,Source File,Category,Runtime Order,Column1,Column2,Column3,Column4
# Environment Settings,,,,,,,,,,,,,
main,init,development_mode,bool,FALSE,yes,Development mode flag,main.py,environment,1,,,,
main,init,logging.level,str,INFO,no,Logging level for application,main.py,environment,2,,,,
main,init,dev_settings_path,Path,config/development.toml,no,Path to development configuration file,config/config_manager.py,environment,1,,,,
update_data,init,env.development_mode,bool,FALSE,yes,Development mode for detailed logging and testing,config/ud_config.py,environment,1,,,,
update_data,init,env.log_level,str,INFO,no,Logging level for the module,config/ud_config.py,environment,1,,,,
,,,,,,,,,,,,,
# OS Settings,,,,,,,,,,,,,
main,init,base_settings_path,Path,config/base.toml,yes,Path to base configuration file,config/config_manager.py,os,1,,,,
main,init,logging.log_dir,Path,LOGS_DIR,yes,Directory for log files,main.py,os,2,,,,
app,paths,LOGS_DIR,Path,~/logs,yes,Directory for application logs,core/config/paths.py,os,1,,,,
app,paths,DATA_DIR,Path,~/data,yes,Directory for application data,core/config/paths.py,os,1,,,,
app,paths,CONFIG_DIR,Path,~/config,yes,Directory for configuration files,core/config/paths.py,os,1,,,,
update_data,init,paths.app_dir,Path,APP_DIR,yes,Application root directory,utils/dw_app_constants.py,os,5,,,,
update_data,init,paths.data_dir,Path,DATA_DIR,yes,Data directory,utils/dw_app_constants.py,os,5,,,,
update_data,init,paths.config_dir,Path,CONFIG_DIR,yes,Configuration directory,utils/dw_app_constants.py,os,5,,,,
update_data,init,paths.profiles_dir,Path,PROFILES_DIR,yes,User profiles directory,utils/dw_app_constants.py,os,5,,,,
update_data,init,paths.backup_dir,Path,~/flatmate/backups,yes,Directory for original file backups,config/ud_config.py,os,1,,,,
update_data,init,paths.master_dir,Path,~/flatmate/master,yes,Directory for master CSV files,config/ud_config.py,os,1,,,,
update_data,init,paths.unrecognized_dir,Path,~/flatmate/unrecognized,yes,Directory for unrecognized files,config/ud_config.py,os,1,,,,
,,,,,,,,,,,,,
# Core Settings,,,,,,,,,,,,,
window,init,app.title,str,FlatMate,yes,Application window title,gui/main_window.py,core_settings,4,,,,
,,,,,,,,,,,,,
# UI Settings,,,,,,,,,,,,,
main,init,ui.theme_profile,str,None,no,UI theme stylesheet path,main.py,ui_settings,3,,,,
main,init,ui.font_size,int,14,no,Default application font size,main.py,ui_settings,3,,,,
main,init,window.size,tuple,"(1200, 800)",no,Initial window dimensions,main.py,ui_settings,4,,,,
window,init,ui.left_panel_width,int,250,no,Initial width of left panel,gui/main_window.py,ui_settings,4,,,,
window,init,ui.right_panel_width,int,250,no,Initial width of right panel,gui/main_window.py,ui_settings,4,,,,
home,init,button.profile.name,str,btn_mngProfile,yes,Profile button object name,modules/home/<USER>
home,init,button.profile.type,str,action,yes,Profile button type property,modules/home/<USER>
home,init,layout.spacing,int,10,no,Layout spacing for panels,modules/home/<USER>
home,init,layout.margins,tuple,"(0,0,0,0)",no,Layout margins for main view,modules/home/<USER>
update_data,init,window.default_size,tuple,"(1200, 800)",no,Default window dimensions,utils/dw_app_constants.py,ui_settings,5,,,,
update_data,init,window.default_font_size,int,14,no,Default font size,utils/dw_app_constants.py,ui_settings,5,,,,
,,,,,,,,,,,,,
# Module Settings,,,,,,,,,,,,,
update_data,init,pipeline.max_file_size,int,10485760,yes,Maximum file size in bytes (10MB),config/ud_config.py,module_settings,1,,,,
update_data,init,pipeline.chunk_size,int,8192,yes,Size of chunks for file hashing,config/ud_config.py,module_settings,1,,,,
update_data,init,pipeline.allowed_extensions,list,[.csv,.xlsx,.xls],yes,Allowed input file extensions,config/ud_config.py,module_settings,1,,
update_data,init,pipeline.required_columns,list,[date,description,amount],yes,Required columns for all formats,config/ud_config.py,module_settings,1,,
update_data,init,pipeline.date_format,str,%Y-%m-%d,yes,Standard date format for processing,config/ud_config.py,module_settings,1,,,,
update_data,init,processing.strict_validation,bool,TRUE,yes,Enable strict data validation,config/ud_config.py,module_settings,2,,,,
update_data,init,processing.auto_backup,bool,TRUE,yes,Automatically backup source files,config/ud_config.py,module_settings,2,,,,
update_data,init,processing.cleanup_source,bool,FALSE,no,Delete source files after successful processing,config/ud_config.py,module_settings,2,,,,
update_data,init,max_file_size,int,100000,yes,Maximum allowed file size in bytes,config/ud_config.py,module_settings,5,,,,
update_data,init,allowed_extensions,list,[.csv,.xlsx,.xls],yes,Allowed file extensions,config/ud_config.py,module_settings,5,,
update_data,init,supported_extensions.data,list,[.csv,.xlsx,.json],yes,Supported data file types,utils/dw_app_constants.py,module_settings,5,,
update_data,init,supported_extensions.config,list,[.json,.yaml,.yml],yes,Supported config file types,utils/dw_app_constants.py,module_settings,5,,
update_data,init,supported_extensions.image,list,[.png,.jpg,.jpeg],yes,Supported image file types,utils/dw_app_constants.py,module_settings,5,,
update_data,init,log_processed_files,bool,TRUE,no,Enable logging of processed files,config/ud_config.py,module_settings,5,,,,
,,,,,,,,,,,,,
# File Tracking,,,,,,,,,,,,,
update_data,init,tracking.max_recent_files,int,10,no,Maximum number of recent files to track,config/ud_config.py,file_tracker,1,,,,
update_data,init,tracking.master_filename,str,fmMaster.csv,yes,Default name for master CSV file,config/ud_config.py,file_tracker,1,,,,
update_data,init,tracking.hash_algorithm,str,md5,yes,Algorithm for file content hashing,config/ud_config.py,file_tracker,1,,,,
update_data,init,recent_sources,list,[],no,List of recently used sources,config/ud_config.py,file_tracker,5,,,,
update_data,init,max_recent_jobs,int,5,no,Maximum number of recent jobs to track,config/ud_config.py,file_tracker,5,,,,
update_data,init,files.max_recent,int,10,no,Maximum number of recent files to track,utils/dw_app_constants.py,file_tracker,5,,,,
file_tracker,init,FM_MASTER_LOCATION,Path,app_data_dir/fm_master_location.json,yes,Location of master file tracking database,config/master_file_tracker.py,file_tracker,2,,,,
file_tracker,update,master_file_history,dict,{},no,History of master file locations and timestamps,config/master_file_tracker.py,file_tracker,2,,,,
,,,,,,,,,,,,,
# State Management,,,,,,,,,,,,,
home,init,app.is_first_run,bool,TRUE,no,Flag for first application run,modules/home/<USER>
,,,,,,,,,,,,,
# User Preferences,,,,,,,,,,,,,
update_data,init,default_source_type,str,folder,no,Default source selection type,config/ud_config.py,user_preferences,5,,,,
update_data,init,strict_validation,bool,TRUE,no,Enable strict file validation,config/ud_config.py,user_preferences,5,,,,
update_data,init,ignore_empty_files,bool,TRUE,no,Skip processing empty files,config/ud_config.py,user_preferences,5,,,,
,,,,,,,,,,,,,
# Data Workflow Core Settings,,,,,,,,,,,,,
,,,,,,,,,,,,,
# Format Definitions,,,,,,,,,,,,,
update_data,init,formats.date_format.kiwibank,str,%d/%m/%Y,yes,Date format for Kiwibank files,utils/formatting/formats/kiwibank.py,module_settings,1,,,,
update_data,init,formats.date_format.anz,str,%d/%m/%Y,yes,Date format for ANZ files,utils/formatting/formats/anz.py,module_settings,1,,,,
update_data,init,formats.encoding,str,utf-8,yes,Default file encoding,utils/formatting/formats/bank_format.py,module_settings,1,,,,
,,,,,,,,,,,,,
# Bank Format Rules,,,,,,,,,,,,,
update_data,init,formats.kiwibank.simple.columns,list,[Account Number,Date,Description,Amount,Balance],yes,Required columns for simple Kiwibank format,utils/formatting/formats/kiwibank.py,module_settings,2
update_data,init,formats.kiwibank.full.columns,list,[Account number,Date,Memo/Description,Amount,Balance],yes,Required columns for full Kiwibank format,utils/formatting/formats/kiwibank.py,module_settings,2
update_data,init,formats.kiwibank.account_pattern,str,^38-\d{4}-\d{7}-\d{2}$,yes,Regex pattern for Kiwibank account numbers,utils/formatting/formats/kiwibank.py,module_settings,2,,,,
,,,,,,,,,,,,,
# Pipeline Settings,,,,,,,,,,,,,
update_data,init,pipeline.batch_size,int,1000,no,Number of rows to process in each batch,utils/dw_pipeline.py,module_settings,3,,,,
update_data,init,pipeline.memory_limit,int,**********,no,Memory limit for processing (1GB),utils/dw_pipeline.py,module_settings,3,,,,
,,,,,,,,,,,,,
# Logging,,,,,,,,,,,,,
update_data,init,logging.level,str,INFO,yes,Log level for data workflow,utils/dw_pipeline.py,logging,1,,,,
update_data,init,logging.pipeline_stats,bool,TRUE,yes,Track and log pipeline statistics,utils/dw_pipeline.py,logging,1,,,,
,,,,,,,,,,,,,
# Paths,,,,,,,,,,,,,
update_data,init,paths.temp_dir,Path,~/flatmate/temp,yes,Directory for temporary processing files,utils/dw_pipeline.py,os,1,,,,
update_data,init,paths.archive_dir,Path,~/flatmate/archive,yes,Directory for processed source files,utils/dw_pipeline.py,os,1,,,,