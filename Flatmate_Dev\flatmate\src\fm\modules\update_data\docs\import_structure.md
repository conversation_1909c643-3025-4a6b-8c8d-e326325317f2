# Import Structure and File Loading Flow

## Current Issues

1. **Circular Imports**
   - `dw_director.py` imports `load_csv_file` from `file_utils`
   - `dw_pipeline.py` also imports from `file_utils`
   - This creates a circular dependency and confusion

2. **Inconsistent Naming**
   - `file_utils.py` defines `load_csv_to_df`
   - `dw_pipeline.py` aliases it as `load_csv_file`
   - `dw_director.py` tries to use `load_csv_to_df` directly

## Proposed Solution

### 1. Import Hierarchy

```
└── update_data/
    ├── utils/
    │   ├── file_utils.py        # Defines load_csv_to_df
    │   ├── dw_pipeline.py       # Imports and re-exports load_csv_to_df as load_csv_file
    │   └── dw_director.py       # Should only import from dw_pipeline
    └── ...
```

### 2. Required Changes

1. **In `dw_pipeline.py`:**
   - Import `load_csv_to_df` from `file_utils`
   - Re-export it as `load_csv_file`
   - Add `shutil` import for file operations

2. **In `dw_director.py`:**
   - Only import `load_csv_file` from `dw_pipeline`
   - Remove direct imports from `file_utils`

3. **In `file_utils.py`:**
   - Keep implementation as is
   - Don't export anything that would create circular dependencies

### 3. Verification Steps

1. Check that all imports point to the right place
2. Verify no circular imports remain
3. Test file loading through the pipeline
4. Ensure all handlers can still access the file loading functionality

## Expected Behavior

1. `dw_director` gets `load_csv_file` from `dw_pipeline`
2. `dw_pipeline` gets `load_csv_to_df` from `file_utils`
3. No direct imports of `file_utils` outside of `dw_pipeline`
4. All file operations go through the pipeline

## Notes

- This maintains backward compatibility
- Keeps file loading logic in one place
- Makes the import hierarchy clearer
- Prevents circular dependencies
