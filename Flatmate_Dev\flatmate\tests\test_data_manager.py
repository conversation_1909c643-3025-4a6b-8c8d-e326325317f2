#!/usr/bin/env python3
"""
Test data management module for flatmate app testing.
Manages temporary copies of real files for testing.
"""

import os
import shutil
import tempfile
from typing import List, Optional
from pathlib import Path

class TestDataManager:
    """Manages temporary copies of files for testing purposes."""
    
    def __init__(self):
        """Initialize the test data manager with a temporary directory."""
        self.test_data_dir = Path(tempfile.mkdtemp(prefix='flatmate_test_'))
        
    def copy_files(self, source_files: List[Path]) -> List[Path]:
        """Copy source files to test directory.
        
        Args:
            source_files: List of paths to source files to copy
            
        Returns:
            List of paths to the copied files
        """
        copied_files = []
        for source in source_files:
            dest = self.test_data_dir / source.name
            shutil.copy2(source, dest)
            copied_files.append(dest)
        return copied_files
    
    def create_backup_dir(self) -> Path:
        """Create a backup directory for testing.
        
        Returns:
            Path to the backup directory
        """
        backup_dir = self.test_data_dir / 'backups'
        backup_dir.mkdir(exist_ok=True)
        return backup_dir
    
    def verify_backup_files(self, source_files: List[Path], backup_dir: Path) -> bool:
        """Verify that backup files exist and match source files.
        
        Args:
            source_files: List of source file paths
            backup_dir: Path to backup directory
            
        Returns:
            True if all backups exist and match, False otherwise
        """
        for source in source_files:
            backup = backup_dir / source.name
            if not backup.exists():
                return False
            
            # Compare file contents
            with open(source, 'rb') as f1, open(backup, 'rb') as f2:
                if f1.read() != f2.read():
                    return False
                
        return True
    
    def cleanup(self):
        """Clean up all test files and directories."""
        if self.test_data_dir.exists():
            shutil.rmtree(self.test_data_dir)
            
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.cleanup()
