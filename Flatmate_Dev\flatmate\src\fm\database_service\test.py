import pandas as pd
import os 

file = os.path.join("/Users/<USER>/DEV/a_CODING_PROJECTS/a_flatmate_App_DEV/flatmate/test_CSV's", "fmMaster.csv")

# Load the CSV file into a DataFrame
df = pd.read_csv(file)

# Display the first few rows
#print(df.head())

# Get info about the DataFrame
print("\nDataFrame Info:")
print(df.info())
print(df.shape)
print(df.dtypes)

# I want to get the earliest date to the latest date
print("\nDate Range:")
print(df['Date'].min())
print(df['Date'].max())

# Get unique date formats in date column
print("\nUnique date formats:")
if 'Date' in df.columns:
    # Convert all dates to strings for analysis
    date_strings = df['Date'].astype(str)
    
    # Extract unique formats by looking at patterns
    unique_formats = {}
    for date_str in date_strings:
        # Skip NaN values
        if date_str == 'nan' or date_str == 'NaT':
            continue
            
        # Try to determine format based on patterns
        if len(date_str) == 10 and date_str[4] == '-' and date_str[7] == '-':
            # YYYY-MM-DD
            format_type = 'ISO (YYYY-MM-DD)'
        elif len(date_str) == 8 and date_str[2] == '/' and date_str[5] == '/':
            # DD/MM/YY
            format_type = 'UK Short (DD/MM/YY)'
        elif len(date_str) == 10 and date_str[2] == '/' and date_str[5] == '/':
            # DD/MM/YYYY
            format_type = 'UK Long (DD/MM/YYYY)'
        elif len(date_str) == 8 and date_str[2] == '-' and date_str[5] == '-':
            # DD-MM-YY
            format_type = 'UK Dash Short (DD-MM-YY)'
        elif len(date_str) == 10 and date_str[2] == '-' and date_str[5] == '-':
            # DD-MM-YYYY
            format_type = 'UK Dash Long (DD-MM-YYYY)'
        else:
            format_type = f'Unknown ({date_str})'
            
        if format_type not in unique_formats:
            unique_formats[format_type] = []
        if len(unique_formats[format_type]) < 3:  # Keep up to 3 examples
            unique_formats[format_type].append(date_str)
    
    # Print results
    for format_type, examples in unique_formats.items():
        print(f"  {format_type}: {examples}")
    print(f"  Total unique formats: {len(unique_formats)}")
    
    # Count occurrences of each format
    format_counts = {}
    for date_str in date_strings:
        if date_str == 'nan' or date_str == 'NaT':
            if 'NULL/NaN' not in format_counts:
                format_counts['NULL/NaN'] = 0
            format_counts['NULL/NaN'] += 1
            continue
            
        if len(date_str) == 10 and date_str[4] == '-' and date_str[7] == '-':
            format_type = 'ISO (YYYY-MM-DD)'
        elif len(date_str) == 8 and date_str[2] == '/' and date_str[5] == '/':
            format_type = 'UK Short (DD/MM/YY)'
        elif len(date_str) == 10 and date_str[2] == '/' and date_str[5] == '/':
            format_type = 'UK Long (DD/MM/YYYY)'
        elif len(date_str) == 8 and date_str[2] == '-' and date_str[5] == '-':
            format_type = 'UK Dash Short (DD-MM-YY)'
        elif len(date_str) == 10 and date_str[2] == '-' and date_str[5] == '-':
            format_type = 'UK Dash Long (DD-MM-YYYY)'
        else:
            format_type = 'Unknown'
            
        if format_type not in format_counts:
            format_counts[format_type] = 0
        format_counts[format_type] += 1
    
    print("\nFormat counts:")
    for format_type, count in format_counts.items():
        print(f"  {format_type}: {count} ({count/len(date_strings)*100:.1f}%)")
else:
    print("No 'Date' column found in the DataFrame")
