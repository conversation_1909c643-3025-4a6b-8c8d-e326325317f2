# Notes
11:04 PM, 18 Jun 2025
okay so about this pyside documentation  i see its half done .. what remains ?
We have sometimes used a python layer to create classes of objects 
like the re usable widgets in gui _shared_components 
(the underscore is also inconsistently applied, usuallyjust to get things sitting in the file tree nicely .. kind of rediculous ...

@c:\Users\<USER>\_DEV\__PROJECTS/Flatmate_Dev\flatmate\src\fm\gui\_shared_components/ but the @ system doesnt seem to mind 




12:27 AM, 18 Jun 2025

tidy up left panel buttons in categorize - should be using predefined types - either in shared components - or the qss types ( not discoverable by type hints unfortunately ) 

12:18 AM, 18 Jun 2025

notes : it may be worth checking whetehr there is a base class in gui shared / components or bases that handles cleaning up signals etc these signals issues ..  that could be a thought for another day ...

(re clenaing up gui shared ui components)

01:50 PM, 17 Jun 2025

long term, lose left panel, modify home module... which becomes dash board, the entry point for the app, on first run the app checks if there is anything in the database, if not the app opens at update_data
which could be called "import_data" or get data, or import transactions or get transactions,

## cat_trans module

- (trans_cat?)
- need reizable columns that open at sensible defaults and remember last setting (module config)
- need side panels to be collapsible
- need to be able to add new tags to transactions
- can manually edit categories
- should be a drop dwon list in the cell with -- add new category - when clicked makes the cell editable, with active cursor in cell, this becomes the new category -saves context changing
- Cell rows should be highlighted when clicked
- Instead of two buttons - left panel should say
  label: load transactions
  option menu - from file
  - from database

After loading there should be an option to
 label: filter by:
 Option menu: by date range
            - by category
            - by account
            - by tag
            - by amount range
            - by description
            - by notes

 Alternatively or as well, - there could be an option to query the data base for a specific date range in the first place - and then the filters would be applied to that subset

### I note there is a "transaction ID"

 which is simply numbered 1 to n in the order they appear in the database.

 How does this work when new transaztions are added ? simply reassigned ?

#### *important* - some fomrats have a unique transaction ID - in this format, this is the only means of being one hundred percent sure a transaction is a duplicate - because tthere is no balance

- I note that in the current rendering no balance is present - must check if it is in the actual db data
  -We need a show all option
- and or a select collumsn to view option -(check boxes-dynamic -based on current database schema)
- perhaps an option to add custom columns

### We need an option to export the current view to csv or xlsx (excell format)

### we need to consider the default column order - its supposed to be based on the order in standards - meaning changing the order their should chage the default order in the data base and here

### currently no ability to select an entire row and copy it

## we may need to build a custom view table to be used throughout the app

pref with a light and dark mode
ultimately should be customisable or have themes
For now, we will need to break this out of cat_view.py
and into a view_components folder (naming convention)


## 2025-06-19 @ 15:59:55 4:00 pm

todo:
create a tool bar base class 
we want a tool bar that can be placed anywhere easily 
can be vertical or horizontal 
contains different tools
these tools will contain widgets and utils 
- we will want a script tha creates a new tool bar from a template
- it will contain a scripts folder, that folder will create new tools
folder structure, (empty files classes inheriting the proper base classes)



