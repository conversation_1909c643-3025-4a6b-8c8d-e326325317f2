# TODO Tree Usage Guide

## Overview

The TODO Tree extension helps track and manage code tasks and notes throughout the codebase. It scans for special comment tags and displays them in an organized tree view.

## Accessing the TODO Tree

1. Click on the TODO Tree icon in the Activity Bar (left side of VSCode)
2. Alternatively, use the Command Palette (Cmd+Shift+P) and search for "TODO Tree: Show Tree"

## Supported Tags

The following tags are configured for this project:

| Tag | Purpose | Example |
|-----|---------|---------|
| `TODO` | Tasks that need to be completed | `# TODO: Implement settings dialog` |
| `FIXME` | Issues that need fixing | `# FIXME: This function has a memory leak` |
| `BUG` | Known bugs | `# BUG: Crashes when input contains special characters` |
| `HACK` | Temporary workarounds | `# HACK: Using this approach until proper API is available` |
| `NOTE` | Important information | `# NOTE: This value must be between 0-100` |
| `QUESTION` | Areas needing clarification | `# QUESTION: Is this the right approach?` |
| `REVIEW` | Code that needs review | `# REVIEW: Check if this algorithm is efficient` |
| `OPTIMIZE` | Performance improvements | `# OPTIMIZE: This loop could be more efficient` |

## Adding TODOs

To add a new TODO that will be recognized by the extension:

```python
# TODO: Brief description of what needs to be done
```

Make sure there's a space after the colon and that the tag is at the beginning of a comment.

## Formatting Examples

### Python
```python
# TODO: Implement settings dialog
# FIXME: This function has a memory leak
# NOTE: This is an important consideration
```

### QSS
```css
/* TODO: Update these colors for dark mode */
/* OPTIMIZE: Consider using variables for repeated values */
```

### Markdown
```markdown
<!-- TODO: Add more documentation here -->
<!-- REVIEW: Check if this information is still current -->
```

## Configuration

The TODO Tree extension is configured in `.vscode/settings.json` with the following settings:

- Custom highlighting for different tag types
- Case-insensitive tag matching
- Included file types: Python, Markdown, and QSS files
- Status bar showing total number of TODOs

## Best Practices

1. Be specific and concise in your TODO descriptions
2. Include enough context for others to understand the task
3. Consider adding your initials or a date for accountability
4. Mark completed TODOs by removing them or changing them to comments
5. Use the appropriate tag for the situation

## Example Workflow

1. Identify an issue or future enhancement
2. Add a TODO comment with the appropriate tag
3. View all TODOs in the TODO Tree panel to prioritize work
4. Remove the TODO comment when the task is completed

## Existing TODOs

The project currently has several TODOs, primarily in:
- `main_window.py`: UI and architecture improvements
- `update_data` module: Feature enhancements
- Style system: Implementation notes
