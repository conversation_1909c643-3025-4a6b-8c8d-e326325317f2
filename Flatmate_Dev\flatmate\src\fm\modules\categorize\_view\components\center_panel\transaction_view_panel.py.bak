"""Transaction view panel for the Categorize module."""

import pandas as pd
from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import <PERSON><PERSON>oxLayout, QWidget
from fm.core.services.logger import Logger

from fm.gui._shared_components.table_view.fm_table_view import CustomTableView as TableView
from fm.core.standards.simple_column_manager import get_simple_column_manager
from fm.modules.categorize.config import config

# Set up logger
logger = Logger()

class TransactionViewPanel(QWidget):
    """Transaction view panel with enhanced table functionality.

    This panel provides:
    - Enhanced table with filtering, column management, and export
    - Top bar with filter controls and column selection
    - Resizable columns with memory
    - Integration with the column manager system
    """
    
    # Signals
    transaction_selected = Signal(int)
    tags_updated = Signal(int, str)
    
    def __init__(self, parent=None):
        """Initialize the transaction view panel."""
        super().__init__(parent)
        logger.debug("Initializing TransactionViewPanel")
        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        """Initialize the UI components."""
        logger.debug("Setting up TransactionViewPanel UI")

        # Ensure required config defaults exist (no speculation!)
        config.ensure_defaults({
            'categorize.display.table_margin': 2,  # Reduced from 10 to 2
            'categorize.display.show_grid_lines': True,
            'categorize.display.row_height': 25,
            'categorize.display.default_visible_columns': ['date', 'details', 'amount', 'account', 'tags'],  # Pre-selected subset
            'categorize.display.available_columns': ['date', 'details', 'amount', 'account', 'tags', 'category', 'notes']  # All available
            # Note: Column widths now come from StandardColumns.get_standard_column_widths()
        })

        # Use config values
        margin = config.get_value('categorize.display.table_margin', 2)  # Reduced default from 10 to 2

        layout = QVBoxLayout(self)
        layout.setContentsMargins(margin, margin, margin, margin)

        # Create enhanced transaction table with filtering
        logger.debug("Creating EnhancedTableWidget for transactions")
        self.transaction_table = TableView()
        layout.addWidget(self.transaction_table)

        logger.debug("TransactionViewPanel UI setup complete")

    def _connect_signals(self):
        """Connect internal signals."""
        logger.debug("Connecting TransactionViewPanel signals")
        # Connect row selection signal
        self.transaction_table.table_view.row_selected.connect(
            lambda row: self.transaction_selected.emit(row))
        
        # Connect cell edit signal
        self.transaction_table.table_view.cell_edited.connect(
            lambda row, col, value: self._handle_cell_edit(row, col, value))
        
        logger.debug("TransactionViewPanel signals connected")

    def _handle_cell_edit(self, row, col, value):
        """Handle cell edit events."""
        logger.debug(f"Cell edited: row={row}, col={col}, value={value}")
        
        # Get the column name from the model
        df = self.transaction_table.get_dataframe()
        if df.empty:
            return
            
        # Get the display columns from the model
        display_columns = self.transaction_table.model._display_columns
        if col >= len(display_columns):
            return
            
        col_name = display_columns[col]
        logger.debug(f"Edited column: {col_name}")
        
        # If this is the tags column
        if col_name == 'tags':
            # Get the transaction ID from the model
            if not df.empty and row < len(df):
                transaction_id = df.iloc[row].get('id', row)
                logger.debug(f"Tags updated for transaction {transaction_id}: {value}")
                self.tags_updated.emit(transaction_id, value)
    
    def set_transactions(self, df: pd.DataFrame):
        """Set the transactions dataframe to display."""
        logger.debug(f"Setting transactions: {len(df) if df is not None else 0} rows")
        if df is not None and not df.empty:
            # Make a copy to avoid modifying the original
            df_copy = df.copy()

            # Debug column names
            logger.debug(f"Original DataFrame columns: {df_copy.columns.tolist()}")

            # Use simple column manager (follows StandardColumns architecture)
            column_manager = get_simple_column_manager()

            # Prepare DataFrame with all available columns from database schema
            df_ordered, column_mapping, available_columns = column_manager.prepare_dataframe_with_all_columns(
                df_copy, "categorize"
            )
            logger.debug(f"Available columns from database: {available_columns}")
            logger.debug(f"Final DataFrame columns: {df_ordered.columns.tolist()}")
            logger.debug(f"Column mapping: {column_mapping}")

            # Set ALL available columns in table model (not just visible ones)
            self.transaction_table.set_dataframe(df_ordered)
            self.transaction_table.set_display_columns(available_columns, column_mapping)

            # Set editable columns (simplified - just tags for now)
            editable_columns = ['tags']  # Simplified for now
            logger.debug(f"Setting editable columns: {editable_columns}")
            self.transaction_table.set_editable_columns(editable_columns)

            # Set column widths using display names
            self._apply_column_widths(column_mapping)

            # Apply default column visibility (hide non-default columns)
            self._apply_default_column_visibility()

            logger.debug("Transactions set successfully")

    def _apply_default_column_visibility(self):
        """Apply default column visibility from config."""
        try:
            # Get default visible columns from config (the pre-selected subset)
            default_visible_columns = config.get_value('categorize.display.default_visible_columns', [])
            available_columns = config.get_value('categorize.display.available_columns', [])

            if not default_visible_columns:
                logger.debug("No default visible columns configured, showing all available")
                return

            logger.debug(f"Applying default column visibility: {default_visible_columns}")
            logger.debug(f"Available columns: {available_columns}")

            # Get the table view and model
            table_view = self.transaction_table.table_view
            model = table_view._model

            # Since we now have ALL available columns in the model in order,
            # we can directly map column indices to database names
            for col_idx, db_name in enumerate(available_columns):
                if col_idx < model.columnCount():
                    # Show column if it's in the default visible list
                    should_show = db_name in default_visible_columns
                    table_view.setColumnHidden(col_idx, not should_show)

                    if should_show:
                        logger.debug(f"Showing column {col_idx}: {db_name}")
                    else:
                        logger.debug(f"Hiding column {col_idx}: {db_name}")

        except Exception as e:
            logger.error(f"Error applying default column visibility: {e}")

    def _apply_column_widths(self, column_mapping):
        """Apply column widths using StandardColumns as single source of truth."""
        try:
            from fm.core.standards.fm_standard_columns import StandardColumns

            # Get standard widths from StandardColumns (canonical source)
            standard_widths = StandardColumns.get_standard_column_widths()

            # Create width mapping using display names (what the table expects)
            width_map = {}

            # Map database column names to display names and apply standard widths
            db_to_display = column_mapping  # This is already db_name -> display_name

            for db_name, display_name in db_to_display.items():
                if display_name in standard_widths:
                    width_map[display_name] = standard_widths[display_name]
                    logger.debug(f"Setting standard width for '{display_name}': {standard_widths[display_name]} characters")
                else:
                    # Fallback for non-standard columns
                    width_map[display_name] = 20  # Default width
                    logger.debug(f"Setting default width for '{display_name}': 20 characters")

            # Apply the widths to the table
            self.transaction_table.set_column_widths(width_map)
            logger.debug(f"Applied column widths: {width_map}")

        except Exception as e:
            logger.error(f"Error applying column widths: {e}")

    def _save_column_selections(self):
        """Save current column selections to local config."""
        try:
            table_view = self.transaction_table.table_view
            model = table_view._model

            # Get currently visible columns (display names)
            visible_display_names = []
            for col_idx in range(model.columnCount()):
                if not table_view.isColumnHidden(col_idx):
                    col_name = model.headerData(col_idx, Qt.Horizontal)
                    visible_display_names.append(str(col_name))

            # Convert display names back to database names for storage
            column_manager = get_simple_column_manager()
            column_mapping = column_manager.get_column_display_mapping("categorize")
            # Reverse the mapping: display_name -> db_name
            display_to_db = {v: k for k, v in column_mapping.items()}

            visible_db_names = []
            for display_name in visible_display_names:
                db_name = display_to_db.get(display_name)
                if db_name:
                    visible_db_names.append(db_name)
                else:
                    # Fallback: convert display name back to db format
                    visible_db_names.append(display_name.lower().replace(' ', '_'))

            # Save to config as last_used_columns (not overriding defaults)
            config.ensure_defaults({
                'categorize.display.last_used_columns': visible_db_names
            })

            logger.debug(f"Saved column selections: {visible_db_names}")

        except Exception as e:
            logger.error(f"Error saving column selections: {e}")

    def disconnect_signals(self):
        """Clean up signal connections."""
        logger.debug("Disconnecting TransactionViewPanel signals")
        if hasattr(self, 'transaction_table') and hasattr(self.transaction_table, 'table_view'):
            try:
                self.transaction_table.table_view.row_selected.disconnect()
                self.transaction_table.table_view.cell_edited.disconnect()
            except (TypeError, RuntimeError) as e:
                # Signal might not be connected
                logger.debug(f"Error disconnecting signals: {e}")








