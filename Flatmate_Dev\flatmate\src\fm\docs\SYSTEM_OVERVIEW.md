# Flatmate App System Overview

## Introduction

This document provides a comprehensive overview of the Flatmate App architecture, components, and design principles. It serves as the central reference point for understanding the system as a whole.

## System Architecture

The Flatmate App follows a modular, feature-driven architecture with clean boundaries between components. The system is organized into the following key areas:

### Core Architecture

The application is built on a modular architecture with the following key principles:

- **Clean Architectural Boundaries** - Components interact through well-defined interfaces
- **Pub/Sub Event System** - Components communicate via an event bus using publish/subscribe pattern
- **Module System** - Features are encapsulated in self-contained modules
- **Service-Oriented Design** - Common functionality is provided through services

### Module Structure

Each module follows a consistent structure:
- **Presenter** - Contains business logic and coordinates between model and view
- **View** - UI components with clean interfaces to the presenter
- **Model** - Data structures and business logic
- **Services** - Shared functionality used by the module

## Key Components

### Event System

The application uses a pub/sub event system where:
- Components publish events to the event bus
- Interested components subscribe to relevant events
- This maintains separation of concerns and clean architectural boundaries

### UI System

The UI is built using Qt with:
- Consistent styling through a theme system
- QStatusBar for application status information
- Clean interfaces between UI components and business logic

### Update Data Module

A core module responsible for:
- File selection and processing
- Progress tracking and status reporting
- Error handling and user feedback

## Documentation Structure

Documentation is organized as follows:

1. **Module-Level READMEs** - Located in each module directory, describing the specific module's purpose and components
2. **Architecture Documentation** - Located in `/flatmate/src/fm/docs/architecture`, covering system-wide architectural decisions
3. **UI System Documentation** - Located in `/flatmate/src/fm/docs/ui_system`, covering UI components and styling
4. **Development Guides** - Located in `/flatmate/src/fm/docs/development`, covering development processes and standards

## Related Documentation

- [Architectural Approach](/ARCHITECTURAL_APPROACH.md) - High-level architectural principles
- [Module System](/DOCS/architecture/module_system.md) - Details on the module system
- [Event Bus Design](/DOCS/architecture/event_bus_design.md) - Event system architecture
- [UI System Design](/DOCS/UI_system_design/UI_system_design.md) - UI component architecture

## Development Principles

1. **Clean Code** - Simple, readable, explicit, and maintainable
2. **Error Handling** - Limited to user input, errors should be visible and immediate
3. **Self-Documenting Code** - Clear, explicit code with concise comments
4. **Proper Interfaces** - Well-defined boundaries between components
